<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" /><span style="margin-left: 10px"
        >长传链路故障处理流程</span
      >
    </div>
    <div style="padding: 0 300px; margin-top: 100px">
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤一"></el-step>
        <el-step title="步骤二"></el-step>
        <el-step title="步骤三"></el-step>
      </el-steps>
    </div>
    <div
      style="width: 440px; margin: 0 auto; margin-top: 100px"
      v-if="active == 1"
    >
      <div class="bt-title">项目经理向省分中心以及国家中心维护组报备</div>
    </div>
    <div
      style="width: 600px; margin: 0 auto; margin-top: 100px"
      v-if="active == 2"
    >
      <div class="bt-title" style="position: relative; text-align: center">
        项目经理找到发生故障的长传链路的调单，记录调单号。
      </div>

      <div style="margin-top: 20px; justify-content: center">
        <div style="display: flex; margin-top: 20px; justify-content: center">
          <div class="label-title">调单号：&nbsp;&nbsp;&nbsp;</div>
          <div style="margin-left: 10px">
            <el-input v-model="form.adjustmentNumber" size="mini"></el-input>
          </div>
        </div>
        <div style="display: flex; margin-top: 20px; justify-content: center">
          <div class="label-title">场所编号：</div>
          <div style="margin-left: 10px">
            <!-- <el-input v-model="form.computerRoomCode" size="mini"></el-input> -->
            <el-select
              v-model="form.computerRoomCode"
              clearable
              @change="jfbhChange"
              placeholder="请选择类型"
              class="widthx"
              size="mini"
            >
              <el-option
                v-for="item in jfList"
                :label="item.computerRoomCode"
                :value="item"
                :key="item.computerRoomCode"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div style="display: flex; margin-top: 20px; justify-content: center">
          <div class="label-title">场所名称：</div>
          <div style="margin-left: 10px">
            <el-input
              disabled
              v-model="form.computerRoomName"
              size="mini"
            ></el-input>
          </div>
        </div>
        <div style="display: flex; margin-top: 20px; justify-content: center">
          <div class="label-title">故障链路经过：</div>
          <div style="margin-left: 10px">
            <el-radio v-model="form.passThrough" label="1">省中心</el-radio>
            <el-radio v-model="form.passThrough" label="2">省移动</el-radio>
          </div>
        </div>
      </div>
    </div>
    <div
      style="width: 718px; margin: 0 auto; margin-top: 100px"
      v-if="active == 3"
    >
      <div class="bt-title" v-if="form.passThrough == 1">
        如果故障链路经过省移动公司的波分设备，协调省移动公司长传业务接口人，处理波分设备以及链路故障。
      </div>
      <div class="bt-title" v-if="form.passThrough == 2">
        如果故障链路经过本系统的波分设备，协调波分设备厂家工程师携带配件到现场处理波分设备故障；链路故障由省移动公司长传业务接口人负责处理。同时，项目经理做全程旁站陪同。
      </div>
    </div>
    <div
      style="
        display: flex;
        width: 500px;
        justify-content: center;
        margin: 0 auto;
        margin-top: 60px;
      "
    >
      <div class="buttonw btnc" v-if="active != 1" @click="syb">上一步</div>
      <div class="buttonw btnc1" v-if="active != 3" @click="xyb">下一步</div>
      <div class="buttonw btnc2" v-if="active == 3" @click="qygddy">
        故障处理单打印
      </div>
      <div class="buttonw btnc3" @click="ccllFh">返回</div>
    </div>
    <el-dialog
      title="迁移工单"
      :visible.sync="qydialogVisible"
      @close="close('formName')"
      width="30%"
    >
      <el-form
        ref="formName"
        :rules="qyRules"
        :model="formqy"
        size="mini"
        label-width="180px"
      >
        <el-form-item label="故障处理截至时间" prop="maintenanceTime">
          <el-date-picker
            v-model="formqy.maintenanceTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="print('formName')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" ref="dialog" width="28.7%">
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="场所巡检工单图标"
        />
        {{ dialogTitle }}
      </template>
      <div class="formDialog">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">场所编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">场所地点：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.locationName }}</div>
        </div>
        <!-- <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">设备类型：</div>
          <div class="formDialogCon">
            {{ this.xjgddyQbj.equipmentTypeName }}
          </div>
        </div> -->
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">设备编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.equipmentCode }}</div>
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">故障处理时间：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.maintenanceTime }}</div>
        </div>
      </div>
      <div class="fjx"></div>
      <div class="formDialog formDialog1">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">提交人：</div>
          <div class="formDialogCon1">{{ this.xjgddyQbj.createByName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <!-- <div class="formDialogItem">负责人：</div>
          <div class="formDialogCon1"></div> -->
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">打印时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.printTime }}
          </div>
        </div>
        <div class="ewm">
          <img :src="img" width="130" height="130" />
        </div>
      </div>
      <div class="btnRight">
        <el-button
          @click="printToPDF"
          type="primary"
          class="btnRightItem"
          size="mini"
          >打 印</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  faultHandlingProcess,
  getComputerRoomList,
  selectFaultHandlingProcess,
  downloadFaultHandling,
  faultHandling,
} from "../../../../api/shma";
import AraleQRCode from "arale-qrcode";

export default {
  components: {},
  props: {},
  data() {
    return {
      active: 1,
      form: {
        adjustmentNumber: "",
        computerRoomCode: "",
        computerRoomName: "",
        passThrough: "1",
        equipmentCode: this.$route.query.equipmentCode,
        computerRoomId: "",
      },
      jfList: [],
      selectedItem: null, // 初始化为 null 或者一个空对象
      id: this.$route.query.id,
      dialogVisible: false, //弹框
      qydialogVisible: false, //迁移工单打印弹框
      formqy: {
        maintenanceTime: "",
      },
      dialogTitle: "",
      img: "",
      xjgddyQbj: {},
      qyRules: {
        maintenanceTime: [
          {
            required: true,
            message: "请选择故障处理截至时间",
            trigger: "change",
          },
        ],
      },
      maintenanceId: "",
    };
  },
  computed: {},
  mounted() {
    console.log(this.active, "active");
    this.selectFaultHandlingProcess();
    this.getComputerRoomList();
  },
  methods: {
    qygddy() {
      this.qydialogVisible = true;
    },
    async print(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.faultHandlingProcess();
          this.dialogVisible = true;
          this.dialogTitle = "设备故障处理工单";
          let params = this.formqy;
          params.equipmentCode = this.form.equipmentCode;
          params.maintenanceId = this.maintenanceId;
          let data = await faultHandling(params);
          console.log(data);
          if (data.code == 10000) {
            this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
            this.makeCode(data.data.scanCode); // 生成二维码
            this.$message.success("成功获取故障处理工单信息!");
            this.qydialogVisible = false;
          } else {
            this.$message.error("提交失败");
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //生成二维码方法
    makeCode(item) {
      const result = new AraleQRCode({
        render: "svg", // 定义生成的类型 'svg' or 'table dom’
        text: item, // 二维码的链接
        size: 150, //二维码大小
      });

      // 将svg xml文档转换成字符串
      const svgXml = new XMLSerializer().serializeToString(result);

      // 将svg字符串转成base64格式，通过 window.btoa方法创建一个 base-64 编码的字符串，进行二次编码解码(encodeURIComponent 字符串进行编码和解码，unescape 进行解码)。
      const src =
        "data:image/svg+xml;base64," +
        window.btoa(unescape(encodeURIComponent(svgXml)));

      // 本地存储图片
      localStorage.setItem("image", src);
      this.getImg();
    },

    // 获取存储的图片给到页面
    getImg() {
      this.img = localStorage.getItem("image");
      console.log(this.img);
      localStorage.removeItem("image");
    },
    async printToPDF() {
      let data = await downloadFaultHandling(this.xjgddyQbj);
      this.dom_download(data, "设备故障处理工单.docx");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    async selectFaultHandlingProcess() {
      const res = await selectFaultHandlingProcess({ id: this.id });
      console.log(res);
      this.active = res.data.maintenanceStatus + 1;
      this.form.adjustmentNumber = res.data.adjustmentNumber;
      this.form.computerRoomCode = res.data.computerRoomCode;
      this.form.computerRoomName = res.data.computerRoomName;
      this.form.passThrough = res.data.passThrough;
      this.form.computerRoomId = res.data.computerRoomId;
      this.maintenanceId = res.data.maintenanceId;
    },
    //获取场所列表
    async getComputerRoomList() {
      const res = await getComputerRoomList();
      this.jfList = res.data;
    },
    jfbhChange(val) {
      console.log(val);
      this.selectedItem = val; // 确保 selectedItem 被正确赋值
      if (val) {
        this.form.computerRoomId = val.id; // 假设场所对象中有 id 字段
        this.form.computerRoomCode = val.computerRoomCode;
        this.form.computerRoomName = val.computerRoomName;
        // 其他字段的赋值...
      } else {
        this.form.computerRoomId = "";
        this.form.computerRoomCode = "";
        this.form.computerRoomName = "";
        this.selectedItem = null;
      }
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    xyb() {
      console.log(this.active, "++");
      if (this.active == 2) {
        if (this.form.adjustmentNumber != "") {
          if (this.form.computerRoomCode != "") {
            this.active++;
            this.faultHandlingProcess();
          } else {
            this.$message.warning("请选择场所编号");
          }
        } else {
          this.$message.warning("请填写调单号");
        }
      } else {
        this.active++;
        this.faultHandlingProcess();
      }
    },
    syb() {
      this.active--;
      if (this.active == 1) {
        this.form.adjustmentNumber = "";
        this.form.computerRoomCode = "";
        this.form.computerRoomName = "";
        this.form.passThrough = "1";
        this.form.computerRoomId = "";
      }
      console.log(this.active, "--");
      this.faultHandlingProcess();
    },
    async faultHandlingProcess() {
      let params = this.form;
      params.equipmentCode = this.form.equipmentCode;
      params.maintenanceId = this.maintenanceId;
      console.log(this.active);
      if (this.active == 2) {
        params.maintenanceStatus = "1";
      } else if (this.active == 3) {
        params.maintenanceStatus = "2";
      } else {
        params.maintenanceStatus = "";
      }
      if (this.dialogVisible == true) {
        params.maintenanceStatus = "3";
      }
      let data = await faultHandlingProcess(params);
      this.maintenanceId = data.data;
      console.log(data, "data");
    },
    ccllFh() {
      this.$router.push("/ccllgl");
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/ .el-step__icon {
  width: 36px;
  height: 36px;
}
/deep/ .el-step.is-horizontal .el-step__line {
  top: 18px;
  left: 45px;
  right: 12px;
}
/deep/ .el-step__head.is-process {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-wait {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-wait {
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  color: #080808;
  font-weight: 500;
}
/deep/ .el-step__title.is-process {
}
/deep/ .el-step__icon.is-text {
  border: 2px solid;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-success .is-text {
  background-color: #0077ff;
}
/deep/ .el-step__head.is-success {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-success {
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  color: #080808;
  font-weight: 500;
}
.buttonw {
  /* width: 72px;
  height: 32px; */
  padding: 0px 20px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.btnc {
  background-color: #3ecbfe;
}
.btnc1 {
  background-color: #3e9efe;
  margin-left: 20px;
}
.btnc2 {
  background-color: #20bdd1;
  margin-left: 20px;
}
.btnc3 {
  background-color: #3e9efe;
  margin-left: 20px;
}
.bt-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
.widthx {
  width: 178px;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 115px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}
</style>
