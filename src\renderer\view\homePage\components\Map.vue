<template>
  <div 
    ref="chartContainer" 
    :style="{ width: width, height: height }"
    class="province-map-container"
  ></div>
</template>

<script>
import * as echarts from 'echarts'
import heilongjiang from "../../../assets/mapJson/230000.js";
import xinjiang from "../../../assets/mapJson/650000.js";
export default {
  name: 'ProvinceMap',
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    provinceName: {
      type: String,
      default: ''
    },
    mapData: {
      type: Array,
      default: () => [
        { name: "哈尔滨市", value: [126.5358, 45.8038, 28, 34, 24, 22] },
        { name: "齐齐哈尔市", value: [123.9182, 47.3543, 34, 40, 30, 36] },
        { name: "牡丹江市", value: [129.6332, 44.5516, 26, 32, 27, 24] },
        { name: "佳木斯市", value: [130.3616, 46.8096, 27, 30, 22, 24] },
        { name: "大庆市", value: [125.1031, 46.5893, 30, 34, 32, 26] },
        { name: "鸡西市", value: [130.9418, 45.2952, 28, 34, 26, 30] },
        { name: "双鸭山市", value: [131.1591, 46.6469, 26, 30, 24, 20] },
        { name: "伊春市", value: [128.8993, 47.7248, 26, 30, 24, 20] },
        { name: "七台河市", value: [131.0031, 45.7717, 34, 38, 30, 28] },
        { name: "鹤岗市", value: [130.2979, 47.3499, 32, 40, 30, 28] },
        { name: "黑河市", value: [127.5285, 50.2452, 30, 38, 34, 36] },
        { name: "绥化市", value: [126.9891, 46.6348, 28, 30, 20, 26] }
      ]
    },
    geoJsonUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      geoCoordMap: this.provinceName != '黑龙江' ? {
        "乌鲁木齐市": [87.6168, 43.8256],
        "克拉玛依市": [84.8739, 45.5886],
        "吐鲁番市": [89.1841, 42.9476],
        "哈密市": [93.5138, 42.8332],
        "昌吉回族自治州": [87.3043, 44.0153],
        "博尔塔拉蒙古自治州": [82.0748, 44.9033],
        "巴音郭楞蒙古自治州": [86.1477, 41.7686],
        "阿克苏地区": [80.2698, 41.1717],
        "克孜勒苏柯尔克孜自治州": [76.1728, 39.7153],
        "喀什地区": [75.9891, 39.4677],
        "和田地区": [79.9253, 37.1107],
        "伊犁哈萨克自治州": [81.3179, 43.9219],
        "塔城地区": [82.9857, 46.7463],
        "阿勒泰地区": [88.1396, 47.8484],
        "石河子市": [86.0411, 44.3059],
        "阿拉尔市": [81.2859, 40.5419],
        "图木舒克市": [79.0784, 39.8673],
        "五家渠市": [87.5269, 44.1664],
        "铁门关市": [85.5012, 41.8273]
      } : {
        哈尔滨市: [126.5358, 45.8038],
        齐齐哈尔市: [123.9182, 47.3543],
        牡丹江市: [129.6332, 44.5516],
        佳木斯市: [130.3616, 46.8096],
        大庆市: [125.1031, 46.5893],
        鸡西市: [130.9418, 45.2952],
        双鸭山市: [131.1591, 46.6469],
        伊春市: [128.8993, 47.7248],
        七台河市: [131.0031, 45.7717],
        鹤岗市: [130.2979, 47.3499],
        黑河市: [127.5285, 50.2452],
        绥化市: [126.9891, 46.6348]
      },
      iconUrl: "image://data:image/png;base64,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"
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    mapData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.loadMapData()
    },
    
    loadMapData() {
      // console.log(this.provinceName, '120---')
      const mapName = this.provinceName != '黑龙江' ? 'xinjiang' : 'heilongjiang'
      const mapData = this.provinceName != '黑龙江' ? xinjiang : heilongjiang
      echarts.registerMap(mapName, mapData)
      this.setChartOption()
    },
    
    setChartOption() {
      const option = {
        backgroundColor: this.backgroundColor,
        animation: false,
        tooltip: {
          show: false,
        },
        geo: this.createGeoLayers(),
        series: this.createSeries()
      }
      
      this.chart.setOption(option)
    },
    
    createGeoLayers() {
      return [
        // 主地图层
        {
          layoutCenter: ['50%', '50%'],
          layoutSize: '150%',
          show: true,
          map: this.provinceName != '黑龙江' ? 'xinjiang' : 'heilongjiang',
          roam: false,
          zoom: 0.65,
          aspectScale: 0.9,
          label: {
            normal: {
              show: true,
              textStyle: {
                color: '#fff'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                color: '#fff'
              }
            }
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: "linear",
                x: 1200,
                y: 0,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0,
                  color: "rgba(3,27,78,0.75)",
                }, {
                  offset: 1,
                  color: "rgba(58,149,253,0.75)",
                }],
                global: true,
              },
              borderColor: "#c0f3fb",
              borderWidth: 1,
              shadowColor: "#8cd3ef",
              shadowOffsetY: 10,
              shadowBlur: 20,
            },
            emphasis: {
              areaColor: "rgba(0,254,233,0.6)",
            }
          },
          emphasis: {
            itemStyle: {
              areaColor: '#59FFCE'
            },
          },
          zlevel: 3
        },
        // 3D效果层
        ...this.create3DLayers()
      ]
    },
    
    create3DLayers() {
      const layers = []
      const configs = [
        { zlevel: -1, layoutCenter: ["50%", "51%"], borderColor: "rgba(58,149,253,0.8)", shadowColor: "rgba(172, 122, 255,0.5)" },
        // { zlevel: -2, layoutCenter: ["50%", "52%"], borderColor: "rgba(58,149,253,0.6)", shadowColor: "rgba(65, 214, 255,1)" },
        // { zlevel: -3, layoutCenter: ["50%", "53%"], borderColor: "rgba(58,149,253,0.4)", shadowColor: "rgba(58,149,253,1)" },
        { zlevel: -4, layoutCenter: ["50%", "52%"], borderColor: "rgba(5,9,57,0.8)", shadowColor: "rgba(29, 111, 165,0.8)", borderWidth: 5 }
      ]
      
      configs.forEach(config => {
        layers.push({
          type: "map",
          map: this.provinceName != '黑龙江' ? 'xinjiang' : 'heilongjiang',
          zlevel: config.zlevel,
          aspectScale: 0.9,
          zoom: 0.65,
          layoutCenter: config.layoutCenter,
          layoutSize: "150%",
          roam: false,
          silent: true,
          itemStyle: {
            normal: {
              borderWidth: config.borderWidth || 1,
              borderColor: config.borderColor,
              shadowColor: config.shadowColor,
              shadowOffsetY: 5,
              shadowBlur: 15,
              areaColor: config.zlevel === -4 ? "rgba(5,21,35,0.1)" : "transparent",
            },
          },
        })
      })
      
      return layers
    },
    
    createSeries() {
      const cityData = this.mapData.map(item => ({ name: item.name }))
      const newsCenter = this.mapData.map(item => ({
        name: item.name,
        value: item.value,
        type: "newsCenter",
        // symbolOffset: this.getSymbolOffset(item.name)
      }))
      
      return [
        // 城市标签
        {
          name: "kashi",
          type: "scatter",
          coordinateSystem: "geo",
          showLegendSymbol: false,
          data: cityData,
          label: {
            color: "#fff",
            normal: {
              color: "#fff",
              formatter: "{b}",
              position: "right",
              show: true,
            },
            emphasis: {
              show: false,
            },
          },
          tooltip: {
            show: false,
          },
          animation: false,
        },
        // 图标点
        {
          name: "newsCenter",
          type: "scatter",
          coordinateSystem: "geo",
          data: newsCenter,
          symbol: this.iconUrl,
          zlevel: 4,
          symbolSize: 60,
          tooltip: {
            show: true,
            trigger: "item",
            triggerOn: "mousemove",
            backgroundColor: "rgba(255, 255, 255, 0)",
            borderColor: "transparent",
            formatter: this.formatTooltip,
            position: this.getTooltipPosition,
          },
          itemStyle: {
            normal: {
              color: "#ea6347",
              borderWidth: 2,
              borderColor: "#f4b391",
            },
          },
          z: 4,
        },
      ]
    },
    
    // getSymbolOffset(name) {
    //   const offsets = {
    //     "巴楚县": [41, 6],
    //     "喀什市": [11, -27],
    //     "麦盖提县": [77, -14],
    //     "莎车县": [-27, -8],
    //     "疏附县": [-28, -20],
    //     "疏勒县": [38, 6],
    //     "塔什库尔干塔吉克自治县": [70, 63],
    //     "叶城县": [-21, 84],
    //     "英吉沙县": [25, -4],
    //     "岳普湖县": [26, -5],
    //     "泽普县": [-3, -12],
    //     "伽师县": [61, -33]
    //   }
    //   return offsets[name] || [0, 0]
    // },
    
    formatTooltip(params) {
      // console.log(params, '301----')
      const arr = params.value
      return `
        <div style="width: 270px;height:auto;padding-top: 10px;padding-bottom: 22px;background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,50,100,0.8) 100%);border-radius: 8px;border: 1px solid #00ffff;">
          <div style="padding:16px 0 0 24px;font-size: 20px;font-family: Microsoft YaHei;font-weight: 600;color: #97FFF7;">${arr[3]}</div>
          <div style="padding:10px 0 0 24px;font-size: 14px;font-family: Microsoft YaHei;font-weight: 400;color: #FFFFFF;">机柜：${Number(arr[2]).toString()}个</div>
        </div>
      `
    },
    
    getTooltipPosition(point, params, dom, rect, size) {
      const pointX = point[0]
      const pointY = point[1]
      const boxWidth = size.contentSize[0]
      const boxHeight = size.contentSize[1]
      const x = pointX - boxWidth / 2
      let y = 0
      
      if (boxHeight > pointY) {
        y = pointY + 40
      } else {
        y = pointY - boxHeight - 40
      }
      
      return [x, y]
    },
    
    updateChart() {
      if (this.chart) {
        this.setChartOption()
      }
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped>
.province-map-container {
  position: relative;
}
</style>