<template>
  <div style="padding: 0px">
    <el-menu
      :default-active="this.$route.path"
      class="el-menu-vertical-demo"
      router
      @open="handleOpen"
      @close="handleClose"
      @select="handleSelect"
      background-color="#3874D5"
      text-color="#FFFFFF"
      active-text-color="#FFFFFF"
    >
      <el-submenu
        v-for="(item, index) in optimizedEnumShowList"
        :index="String(index)"
        :key="`parent-${item.gnzyid || index}`"
        :style="{ '--background': item.background }"
      >
        <template slot="title">
          <div class="menu-title">
            <i :class="item.displayIcon"></i>
            <span>{{ item.name }}</span>
          </div>
        </template>
        <el-menu-item
          v-for="(itemChild, indexChild) in item.children"
          :index="itemChild.path"
          :key="`child-${itemChild.gnzyid || itemChild.path || indexChild}`"
          :route="itemChild.path"
          class="ejcd"
        >
          <div class="menu-item-content">
            <span :class="itemChild.selected ? 'xtb-selected' : 'xtb'"></span>
            <div>{{ itemChild.name }}</div>
          </div>
        </el-menu-item>
      </el-submenu>
    </el-menu>
  </div>
</template>

<script>
import { getAllMenu } from "../../../api/submeu";
import storage from "../../../model/storage";

export default {
  data() {
    return {
      // 需要显示的菜单，由父组件传递过来
      showRoutePathList: [],
      // 菜单数据缓存
      menuDataCache: null,
      // 防抖定时器
      debounceTimer: null,
      // 上次菜单列表的字符串化版本，用于比较
      lastMenuListStr: '',
      // 菜单路由的总集，新增的菜单需要在这里进行配置一下
      enumList: [
        {
          name: "场所管理",
          icon: "icon-01",
          icon_selected: "icon-01-selected",
          selected: true,
          children: [
            {
              name: "场所管理",
              path: "/jfgl",
            },
          ],
        },
        // {
        //   name: "机柜管理",
        //   icon: "icon-02",
        //   icon_selected: "icon-02-selected",
        //   selected: true,
        //   children: [
        //     {
        //       name: "机柜管理",
        //       path: "/jggl",
        //     },
        //   ],
        // },
        {
          name: "资产管理",
          icon: "icon-03",
          icon_selected: "icon-03-selected",
          selected: true,
          children: [
            {
              name: "资产管理",
              path: "/sbgl",
            },
          ],
        },
        {
          name: "介质管理",
          icon: "icon-06",
          icon_selected: "icon-06-selected",
          selected: true,
          children: [
            {
              name: "巡检操作管理",
              path: "/xjczgl",
            },
            {
              name: "迁移操作管理",
              path: "/qyczgl",
            },
            {
              name: "资产变更管理",
              path: "/zcbggl",
            },
            {
              name: "故障处理管理",
              path: "/gzclgl",
            },
            // {
            //   name: "载体管理",
            //   path: "/smzttz",
            // },
            // {
            //   name: "载体制作登记",
            //   path: "/smztzz",
            // },
            // {
            //   name: "载体复制登记",
            //   path: "/smztfz",
            // },
            // {
            //   name: "载体接收传递登记",
            //   path: "/smztjs",
            // },
            // {
            //   name: "载体外发传递登记",
            //   path: "/smztwf",
            // },
            // {
            //   name: "载体签收登记",
            //   path: "/smztqsdj",
            // },
            // {
            //   name: "载体外出携带登记",
            //   path: "/smztwcxd",
            // },
            // {
            //   name: "载体借阅登记",
            //   path: "/smztjy",
            // },
            // {
            //   name: "载体销毁登记",
            //   path: "/smztxh",
            // },
          ],
        },
        {
          name: "数据统计",
          icon: "icon-05",
          icon_selected: "icon-05-selected",
          selected: true,
          children: [
            {
              name: "巡检操作数据统计",
              path: "/jfxjsjtj",
            },
            {
              name: "资产迁移数据统计",
              path: "/sbqysjtj",
            },
            {
              name: "资产变更数据统计",
              path: "/sbxhsjtj",
            },
            {
              name: "故障处理数据统计",
              path: "/gzclsjtj",
            },
            {
              name: "非合规操作",
              path: "/fhgcz",
            },
          ],
        },
        {
          name: "我的工作",
          icon: "icon-03",
          icon_selected: "icon-03-selected",
          selected: false,
          children: [
            {
              name: "待办事项",
              path: "/dbsx",
            },
            {
              name: "已办事项",
              path: "/ybsx",
            },
            {
              name: "我的发起",
              path: "/wdfq",
            },
          ],
        },
      ],
      // 显示菜单
      enumShowList: [],
      enumHiddenList: [],
      dwjy: true,
    };
  },
  props: {},

  computed: {
    // 计算属性用于优化菜单渲染
    optimizedEnumShowList() {
      return this.enumShowList.map(item => ({
        ...item,
        // 预计算一些常用的属性
        hasChildren: item.children && item.children.length > 0,
        displayIcon: item.selected ? item.icon_selected : item.icon
      }));
    }
  },

  methods: {
    async loadMenuData() {
      try {
        // 先尝试从缓存获取
        const cachedData = storage.get('menuData');
        if (cachedData && cachedData.timestamp &&
            Date.now() - cachedData.timestamp < 5 * 60 * 1000) { // 5分钟缓存
          this.menuDataCache = cachedData.data;
          return cachedData.data;
        }

        // 从API获取数据
        const data1 = await getAllMenu();
        if (!data1 || !Array.isArray(data1)) {
          console.warn('菜单数据获取失败或格式错误');
          return [];
        }

        // 构建菜单树结构
        const menuTree = this.buildMenuTree(data1);

        // 缓存数据
        this.menuDataCache = menuTree;
        storage.set('menuData', {
          data: menuTree,
          timestamp: Date.now()
        });

        return menuTree;
      } catch (error) {
        console.error('加载菜单数据失败:', error);
        // 如果API失败，尝试使用缓存数据
        const cachedData = storage.get('menuData');
        return cachedData ? cachedData.data : [];
      }
    },

    buildMenuTree(data) {
      const itemMap = new Map();
      const rootItems = [];

      // 第一遍遍历：创建所有项目的映射
      data.forEach(item => {
        itemMap.set(item.gnzyid, { ...item, children: [] });
      });

      // 第二遍遍历：构建父子关系
      data.forEach(item => {
        const currentItem = itemMap.get(item.gnzyid);
        if (item.fcdid && itemMap.has(item.fcdid)) {
          // 有父级，添加到父级的children中
          itemMap.get(item.fcdid).children.push(currentItem);
        } else {
          // 没有父级，是根节点
          rootItems.push(currentItem);
        }
      });

      return rootItems;
    },

    saveMenuState() {
      const menuState = {
        enumShowList: this.enumShowList,
        showRoutePathList: this.showRoutePathList,
        currentRoute: this.$route.path,
        timestamp: Date.now()
      };
      storage.set('menuState', menuState);
    },

    restoreMenuState() {
      const savedState = storage.get('menuState');
      if (savedState && savedState.timestamp &&
          Date.now() - savedState.timestamp < 30 * 60 * 1000) { // 30分钟有效期

        if (savedState.enumShowList && savedState.enumShowList.length > 0) {
          this.enumShowList = savedState.enumShowList;
        }

        if (savedState.showRoutePathList && savedState.showRoutePathList.length > 0) {
          this.showRoutePathList = savedState.showRoutePathList;
        }
      }
    },

    debouncedMenuUpdate(newVal) {
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 设置新的定时器
      this.debounceTimer = setTimeout(() => {
        this.showRoutePathList = newVal;
        this.menuBuild();
        this.saveMenuState();
      }, 100); // 100ms防抖
    },

    handleOpen() {},
    handleClose() {},
    handleSelect(_, path) {
      const targetPath = Array.isArray(path) ? path[path.length - 1] : path;

      // 找到选中的菜单项
      let selectedParentIndex = -1;
      let selectedChildIndex = -1;

      // 优化查找逻辑，避免多重循环
      for (let i = 0; i < this.enumShowList.length; i++) {
        const parentItem = this.enumShowList[i];
        if (parentItem.children) {
          for (let j = 0; j < parentItem.children.length; j++) {
            if (parentItem.children[j].path === targetPath) {
              selectedParentIndex = i;
              selectedChildIndex = j;
              break;
            }
          }
          if (selectedParentIndex !== -1) break;
        }
      }

      // 如果找到了选中项，更新状态
      if (selectedParentIndex !== -1 && selectedChildIndex !== -1) {
        this.updateMenuSelection(selectedParentIndex, selectedChildIndex);
      }
    },

    updateMenuSelection(parentIndex, childIndex) {
      // 直接修改原数组，避免深拷贝
      this.enumShowList.forEach((item, index) => {
        const isSelectedParent = index === parentIndex;
        item.selected = isSelectedParent;
        item.background = isSelectedParent ? "#115CC1" : "rgba(255,255,255,0)";

        if (item.children) {
          item.children.forEach((childItem, cIndex) => {
            const isSelectedChild = isSelectedParent && cIndex === childIndex;
            childItem.selected = isSelectedChild;
            childItem.background = isSelectedChild ? "#115CC1" : "rgba(255,255,255,0)";
          });
        }
      });
    },
    // 菜单构建方法
    menuBuild() {
      const PubSub = require("pubsub-js");
      PubSub.subscribe("dataNext", (_, data) => {
        if (data == "next") {
          this.dwjy = false;
          this.enumShowList.find((item) => {
            if (item.name == "自查自评") {
              item.children = [
                {
                  name: "自查自评历史",
                  path: "/zczpls",
                },
              ];
            }
          });
        }
      });
      PubSub.subscribe("dataFh", (_, data) => {
        if (data == "fh") {
          this.dwjy = true;
          this.enumShowList.find((item) => {
            if (item.name == "自查自评") {
              item.children = [
                {
                  name: "自查自评历史",
                  path: "/zczpls",
                },
                {
                  name: "继续自查自评",
                  path: "/jxzczp",
                },
                {
                  name: "新建自查自评",
                  path: "/xjzczp",
                },
              ];
            }
          });
        }
      });
      let enumList = JSON.parse(JSON.stringify(this.enumList));
      // let enumList = this.enumLists
      console.log(this.enumList);
      console.log("菜单逻辑 构建菜单 enumList old", enumList);
      this.recursiveTree(enumList);
      // 递归完成，再次筛选一次根节点
      let len = enumList.length - 1;
      for (let i = len; i >= 0; i--) {
        let children = enumList[i];
        if (
          children &&
          (children.children === undefined ||
            children.children == [] ||
            children.children.length <= 0)
        ) {
          // console.log('找到了', children)
          enumList.splice(i, 1);
        }
      }
      this.enumShowList = enumList;
      // this.enumShowList[0].icon = 'el-icon-eleme'
      // this.enumShowList[1].icon = 'el-icon-phone'
      // this.enumShowList[2].icon = 'el-icon-user-solid'
      // this.enumShowList[3].icon = 'el-icon-eleme'
      // this.enumShowList[4].icon = 'el-icon-eleme'
      // this.enumShowList[5].icon = 'el-icon-eleme'
      // this.enumShowList[6].icon = 'el-icon-eleme'
      // this.enumShowList[7].icon = 'el-icon-eleme'
      console.log(this.enumShowList);
      // console.log('菜单逻辑 构建菜单结果', this.enumShowList)
    },
    // 树递归方法开始
    // 优先修剪叶子，没有叶子就剪去树枝，没有树枝则砍去树干
    recursiveTree(treeList) {
      // console.log('treeList', JSON.parse(JSON.stringify(treeList)))

      let len = treeList.length - 1;

      for (let i = len; i >= 0; i--) {
        let item = treeList[i];
        let itemChildren = item.children;
        if (itemChildren && itemChildren != []) {
          this.recursiveTree(itemChildren);
        } else {
          // console.log(item.name, item.path, this.showRoutePathList.indexOf(item.path))
          if (this.showRoutePathList.indexOf(item.path) == -1) {
            treeList.splice(i, 1);
          }
        }
      }
    },
    // 树递归方法结束
  },
  async mounted() {
    // 初始化菜单数据
    await this.loadMenuData();

    // 恢复菜单状态
    this.restoreMenuState();

    console.log(this.$store.default.state.Counter);
  },

  beforeDestroy() {
    // 清除防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // 保存当前菜单状态
    this.saveMenuState();
  },
  watch: {
    "$store.default.state.Counter.elAsideMenuList": {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          const newValStr = JSON.stringify(newVal);
          // 避免重复处理相同的数据
          if (newValStr === this.lastMenuListStr) {
            return;
          }
          this.lastMenuListStr = newValStr;

          console.log("菜单逻辑 elAsideMenuList变化", newVal);
          this.debouncedMenuUpdate(newVal);
        }
      },
      immediate: true
    },
    showRoutePathList(newVal) {
      this.$nextTick(() => {
        // 回抛新菜单
        this.$emit("getNewMenuList", newVal);
      });
    },
    $route: {
      handler(to) {
        if (to.path && this.enumShowList.length > 0) {
          // 优化路由变更处理，直接调用handleSelect
          this.handleSelect(null, to.path);
          // 保存当前路由状态
          this.saveMenuState();
        }
      },
      immediate: true
    },
  },
};
</script>


<style scoped>
.icon-01 {
  /* background-image: url(../../assets/icons/left_icon1_a.png); */
  background-image: url(../../assets/icons/s-icon-17.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-01-selected {
  background-image: url(../../assets/icons/s-icon-17.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-02 {
  background-image: url(../../assets/icons/s-icon-18.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-02-selected {
  background-image: url(../../assets/icons/s-icon-18.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-03 {
  background-image: url(../../assets/icons/s-icon-20.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-03-selected {
  background-image: url(../../assets/icons/s-icon-20.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-04 {
  background-image: url(../../assets/icons/s-icon-22.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-04-selected {
  background-image: url(../../assets/icons/s-icon-22.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-05 {
  background-image: url(../../assets/icons/s-icon-23.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-05-selected {
  background-image: url(../../assets/icons/s-icon-23.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-06 {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-06-selected {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-07 {
  background-image: url(../../assets/icons/s-icon-21.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-07-selected {
  background-image: url(../../assets/icons/s-icon-21.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-08 {
  background-image: url(../../assets/icons/s-icon-25.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-08-selected {
  background-image: url(../../assets/icons/s-icon-25.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-zczp {
  background-image: url(../../assets/icons/zczp.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-zczp-selected {
  background-image: url(../../assets/icons/zczp.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-sjrz {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

.icon-sjrz-selected {
  background-image: url(../../assets/icons/s-icon-24.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 22px;
  height: 22px;
  display: inline-block;
}

/**菜单样式**/
:deep(.el-menu-item).is-active {
  /* background: rgb(217, 236, 255) !important; */
  background: #084daa !important;
}

:deep(.el-menu) .el-menu-item:hover {
  /* background: rgb(217, 236, 255) !important; */
  background: #084daa !important;
}

/* //对应子级，父级的样式 */
:deep(.el-submenu).is-active > .el-submenu__title {
  background: #115bc1 !important;
  /* background: var(--background) !important; */
  color: white !important;
}

:deep(.el-submenu__title) {
  font-size: 18px !important;
}

:deep(.el-submenu).is-active > .el-submenu__title span {
  font-weight: bold;
}

:deep(.el-submenu__title):hover {
  /* background: rgb(160, 207, 255) !important; */
  background: #115bc1 !important;
}

:deep(.el-submenu__title) i {
  color: white;
}

/***/
.menu-title {
  display: flex;
  align-items: center;
}

.menu-item-content {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.xtb {
  display: inline-block;
  width: 7px;
  height: 7px;
  margin-right: 10px;
  background: white;
}

.xtb-selected {
  display: inline-block;
  width: 7px;
  height: 7px;
  margin-right: 10px;
  background: white;
}
</style>
