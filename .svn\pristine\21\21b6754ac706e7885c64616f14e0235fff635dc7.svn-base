<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span
        class="title"
        :class="[sbxxQhVal == 1 ? 'title1' : '']"
        @click="sbxxClick(1)"
        >资产信息</span
      >
      <!-- <span
        class="title"
        :class="[sbxxQhVal == 4 ? 'title1' : '']"
        @click="sbxxClick(4)"
        v-if="tjType == '3'"
        >设备巡检信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 2 ? 'title1' : '']"
        @click="sbxxClick(2)"
        v-if="tjType == '3'"
        >设备轨迹信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 3 ? 'title1' : '']"
        @click="sbxxClick(3)"
        v-if="tjType == '3'"
        >设备故障处理信息</span
      > -->
    </div>
    <div v-if="sbxxQhVal == 1">
      <el-form ref="form" :model="form" size="mini" label-width="120px">
        <div class="flex">
          <el-form-item label="资产主要类型">
            <el-select
              :disabled="disabled"
              v-model="form.equipmentMainType"
              clearable
              @change="sbzylxChange"
              placeholder="请选择设备主要类型"
              class="widthx"
            >
              <el-option
                v-for="item in sbzylxList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备类型" v-if="form.equipmentMainType != 4">
            <el-select
              v-model="form.equipmentType"
              clearable
              :disabled="disabled"
              placeholder="请选择设备类型"
              class="widthx"
            >
              <el-option
                v-for="item in sblxList"
                :label="item.mc"
                :value="item.csz"
                :key="item.csz"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div v-if="form.equipmentMainType == 4">
          <div
            class="flex"
          >
            <el-form-item label="场所编号">
              <el-select
                v-model="form.computerRoomCode"
                clearable
                @change="jfbhChange"
                placeholder="请选择场所编号"
                class="widthx"
              >
                <el-option
                  v-for="item in jfList"
                  :label="item.computerRoomCode"
                  :value="item"
                  :key="item.computerRoomCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="场所名称">
              <el-input v-model="form.computerRoomName" disabled></el-input>
            </el-form-item>
          </div>
          <div
            class="flex"
          >
            <el-form-item label="机柜编号">
              <el-select
                disabled
                v-model="form.cabinetCode"
                clearable
                @change="jgbhChange"
                placeholder="请选择机柜编号"
                class="widthx"
              >
                <el-option
                  v-for="item in jgList"
                  :label="item.cabinet_code"
                  :value="item"
                  :key="item.cabinet_code"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="机柜名称">
              <el-input v-model="form.cabinetName"></el-input>
            </el-form-item>
          </div>
        </div>
        <div v-else>
          <div
            class="flex"
            v-if="form.equipmentMainType == 1 || form.equipmentMainType == 2"
          >
            <el-form-item label="场所编号">
              <el-select
                :disabled="disabled"
                v-model="form.computerRoomCode"
                clearable
                @change="jfbhChange"
                placeholder="请选择场所编号"
                class="widthx"
              >
                <el-option
                  v-for="item in jfList"
                  :label="item.computerRoomCode"
                  :value="item"
                  :key="item.computerRoomCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="场所名称">
              <el-input v-model="form.computerRoomName" disabled></el-input>
            </el-form-item>
          </div>
          <div
            class="flex"
            v-if="form.computerRoomCode && form.equipmentMainType == 1"
          >
            <el-form-item label="机柜编号">
              <el-select
                :disabled="disabled"
                v-model="form.cabinetCode"
                clearable
                @change="jgbhChange"
                placeholder="请选择机柜编号"
                class="widthx"
              >
                <el-option
                  v-for="item in jgList"
                  :label="item.cabinet_code"
                  :value="item"
                  :key="item.cabinet_code"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="机柜名称">
              <el-input v-model="form.cabinetName" disabled></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="flex" v-if="form.equipmentMainType != 4">
          <el-form-item label="设备编号">
            <el-input v-model="form.equipmentCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="设备名称">
            <el-input
              v-model="form.equipmentName"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex" v-if="form.equipmentMainType != 4">
          <el-form-item label="IP">
            <el-input v-model="form.ip" :disabled="disabled"></el-input>
          </el-form-item>
          <el-form-item label="设备型号">
            <el-input
              v-model="form.equipmentModel"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex" v-if="form.equipmentMainType != 4">
          <el-form-item label="设备序列号">
            <el-input
              v-model="form.equipmentSerialNumber"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="使用状态" v-if="tjType != '1'">
            <el-select
              :disabled="disabled"
              v-model="form.useStatus"
              v-if="tjType != '1'"
              clearable
              placeholder="请选择使用状态"
              class="widthx"
            >
              <el-option
                v-for="item in syztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="flex" v-if="form.equipmentMainType != 4">
          <el-form-item label="销毁状态" v-if="tjType != '1'">
            <el-select
              :disabled="true"
              v-model="form.destroyStatus"
              v-if="tjType != '1'"
              clearable
              placeholder="请选择销毁状态"
              class="widthx"
            >
              <el-option
                v-for="item in xhztList"
                :label="item.mc"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <!-- <div class="flex">
          <el-form-item label="审批状态">
            <el-input v-model="form.approveStatus"></el-input>
          </el-form-item>
        </div> -->
        <div class="flex">
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="form.remark"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <div style="width: 950px">
          <el-form-item style="float: right">
            <el-button type="success" @click="add" v-if="tjType == '1'"
              >提交</el-button
            >
            <!-- <el-button type="primary" @click="qygddy(1)" v-if="tjType != '1'"
              >迁移工单打印</el-button
            >
            <el-button type="primary" @click="qygddy(2)" v-if="tjType != '1'"
              >销毁工单打印</el-button
            >
            <el-button type="primary" @click="qygddy(3)" v-if="tjType != '1'"
              >故障处理打印</el-button
            > -->
            <el-button type="primary" @click="update" v-if="tjType == '2'"
              >修改</el-button
            >
            <el-button type="primary" @click="fh">返回</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-dialog
      title="迁移工单"
      :visible.sync="qydialogVisible"
      @close="close('formName')"
      width="30%"
    >
      <el-form
        ref="formName"
        :rules="qyRules"
        :model="formqy"
        size="mini"
        label-width="180px"
      >
        <el-form-item
          label="迁移所在地"
          prop="relocationLocation"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationLocation"
            clearable
            @change="locationChange"
            placeholder="请选择迁移所在地"
            class="widthx"
          >
            <el-option
              v-for="item in locationList"
              :label="item.name"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移所在区县"
          prop="relocationArea"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationArea"
            clearable
            @change="areaChange"
            placeholder="请选择迁移所在区县"
            class="widthx"
          >
            <el-option
              v-for="item in areaList"
              :label="item.name"
              :value="item.code"
              :key="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移所在机构"
          prop="relocationInstitution"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationInstitution"
            clearable
            @change="institutionChange"
            placeholder="请选择迁移所在机构"
            class="widthx"
          >
            <el-option
              v-for="item in institutionList"
              :label="item.dwmc"
              :value="item.dwid"
              :key="item.dwid"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移场所"
          prop="relocationComputerRoomId"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationComputerRoomId"
            clearable
            @change="computerChange"
            placeholder="请选择迁移场所"
            class="widthx"
          >
            <el-option
              v-for="item in computerList"
              :label="item.computerRoomName"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移机柜"
          prop="relocationCabinetId"
          v-if="gdlx == 1"
        >
          <el-select
            v-model="formqy.relocationCabinetId"
            clearable
            placeholder="请选择迁移机柜"
            class="widthx"
          >
            <el-option
              v-for="item in cabinetList"
              :label="item.cabinet_name"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="迁移截至时间"
          prop="relocationTime"
          v-if="gdlx == 1"
        >
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.relocationTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="销毁截至时间" prop="destroyTime" v-if="gdlx == 2">
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.destroyTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="故障处理截至时间"
          prop="maintenanceTime"
          v-if="gdlx == 3"
        >
          <el-date-picker
            :disabled="disabled"
            v-model="formqy.maintenanceTime"
            style="width: 100%"
            clearable
            type="date"
            placeholder="选择时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="print(gdlx, 'formName')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" ref="dialog" width="28.7%">
      <template #title>
        <img
          src="./img/title.png"
          style="margin-right: 15px"
          alt="场所巡检工单图标"
        />
        {{ dialogTitle }}
      </template>
      <div class="formDialog">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">场所编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.computerRoomCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">场所地点：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.locationName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">设备类型：</div>
          <div class="formDialogCon">
            {{ this.xjgddyQbj.equipmentTypeName }}
          </div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">设备编号：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.equipmentCode }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign" v-if="gdlx == 1">
          <div class="formDialogItem">迁移时间：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.relocationTime }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign" v-if="gdlx == 1">
          <div class="formDialogItem">迁出地点：</div>
          <div class="formDialogCon">
            {{ this.xjgddyQbj.reLocationLocationName }}
          </div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign" v-if="gdlx == 2">
          <div class="formDialogItem">销毁时间：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.destroyTime }}</div>
        </div>
        <div class="flexAlign" v-if="gdlx == 3">
          <div class="formDialogItem">故障处理时间：</div>
          <div class="formDialogCon">{{ this.xjgddyQbj.printTime }}</div>
        </div>
      </div>
      <div class="fjx"></div>
      <div class="formDialog formDialog1">
        <div style="margin-bottom: 18px" class="flexAlign">
          <div class="formDialogItem">提交人：</div>
          <div class="formDialogCon1">{{ this.xjgddyQbj.createByName }}</div>
        </div>
        <div style="margin-bottom: 18px" class="flexAlign">
          <!-- <div class="formDialogItem">负责人：</div>
          <div class="formDialogCon1"></div> -->
        </div>
        <div class="flexAlign">
          <div class="formDialogItem">打印时间：</div>
          <div class="formDialogCon1">
            {{ this.xjgddyQbj.printTime }}
          </div>
        </div>
        <div class="ewm">
          <img :src="img" width="130" height="130" />
        </div>
      </div>
      <div class="btnRight" v-if="!isPrinting">
        <el-button
          @click="printToPDF"
          type="primary"
          class="btnRightItem"
          size="mini"
          >打 印</el-button
        >
      </div>
    </el-dialog>
    <div class="scroll-container" style="height: 100%" v-if="sbxxQhVal == 4">
      <el-table
        :data="xjList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8'
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
        class="table"
      >
        <el-table-column
          type="index"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="设备编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="设备名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentType"
          label="设备类型"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentOnline"
          label="设备在线"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentPower"
          label="设备电力"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentRunningSound"
          label="设备运行声音"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentWarm"
          label="设备告警"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentLoosen"
          label="设备松动"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentDowntime"
          label="设备宕机"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentHardDisk"
          label="设备硬盘"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentOhticalModule"
          label="设备光模块"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentPowerModule"
          label="设备电源模块"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentFan"
          label="设备风扇"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentTemperature"
          label="设备温度"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentPort"
          label="设备端口"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentOpticalFiber"
          label="设备光纤"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentNetworkCable"
          label="设备网线"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentLabel"
          label="设备标签"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="description"
          label="故障情况描述"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="treatment"
          label="处理办法"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="recoveryTime"
          label="恢复时间"
          v-if="this.form.equipmentMainType != '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cableDamaged"
          label="线缆破损"
          v-if="this.form.equipmentMainType == '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cableRibbon"
          label="线缆扎带"
          v-if="this.form.equipmentMainType == '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cableJoint"
          label="线缆接头"
          v-if="this.form.equipmentMainType == '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cableLabel"
          label="线缆标签"
          v-if="this.form.equipmentMainType == '2'"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionTime"
          label="巡检时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionCode"
          label="巡检人员编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionName"
          label="巡检人员名称"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 2">
      <el-table
        :data="gjxxList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8'
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="设备编号"
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="设备名称"
        ></el-table-column>
        <el-table-column prop="typeName" label="设备类型"></el-table-column>
        <el-table-column prop="useStatus" label="使用状态"></el-table-column>
        <el-table-column prop="operation" label="轨迹类型"></el-table-column>
        <el-table-column
          prop="operationTime"
          label="操作时间"
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 3">
      <el-table
        :data="gzclList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8'
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="设备编号"
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="设备名称"
        ></el-table-column>
        <el-table-column prop="typeName" label="设备类型"></el-table-column>
        <el-table-column prop="operation" label="处理步骤"></el-table-column>
        <!-- <el-table-column prop="yt" label="处理流程"></el-table-column> -->
        <el-table-column
          prop="operationTime"
          label="操作时间"
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getComputerRoomList,
  getCabinetList,
  insertEquipment,
  updateEquipment,
  getEquipmentType,
  getEquipmentCode,
  downloadMigrateEquipment,
  downloadDestructionEquipment,
  downloadFaultHandling,
  findEquipmentById,
  getMaintenanceStatus,
  getCity,
  getArea,
  getDwxx,
  migrateEquipment,
  destructionEquipment,
  faultHandling,
  selectDestroyEquipmentTrajectoryPage,
  selectEquipmentMaintenanceProcessPage,
  selectEquipmentInspectionProcessPage,
  insertCabinet,
  getCabinetCode // 添加机柜时获取机柜编号
} from "../../../../api/shma";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import AraleQRCode from "arale-qrcode";

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        computerRoomId: "",
        cabinetId: "",
        computerRoomCode: "",
        computerRoomName: "",
        cabinetCode: "",
        cabinetName: "",
        equipmentCode: "",
        equipmentName: "",
        equipmentMainType: "",
        ip: "",
        equipmentType: "",
        equipmentModel: "",
        equipmentSerialNumber: "",
        lastInspectionTime: "",
        lastInspectionResult: "",
        useStatus: 1,
        moveStatus: "",
        destroyStatus: "",
        maintenanceStatus: "",
        approveStatus: "",
        remark: ""
      },
      formqy: {
        relocationLocation: "",
        relocationArea: "",
        relocationInstitution: "",
        relocationComputerRoomId: "",
        relocationCabinetId: "",
        relocationTime: "",
        destroyTime: "",
        maintenanceTime: ""
      },
      sbxxQhVal: 1,
      gjxxList: [],
      gzclList: [],
      xjList: [],
      tjType: "",
      id: "",
      page: 1,
      pageSize: 10,
      total: 0,
      disabled: false,
      isPrinting: false, //是否正在打印
      dialogVisible: false, //弹框
      qydialogVisible: false, //迁移工单打印弹框
      img: "",
      xjgddyQbj: {},
      jfList: [],
      jgList: [],
      selectedItem: null, // 初始化为 null 或者一个空对象
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" }
      ],
      qyztList: [
        { id: 1, mc: "迁移中" },
        { id: 2, mc: "已迁移" },
        { id: 3, mc: "未迁移" }
      ],
      xhztList: [
        { id: 1, mc: "销毁中" },
        { id: 2, mc: "已销毁" },
        { id: 3, mc: "未销毁" }
      ],
      wxztList: [],
      sbzylxList: [
        { id: "1", mc: "设备" },
        { id: "4", mc: "机柜" },
        { id: "2", mc: "线缆" },
        { id: "3", mc: "长传链路" }
      ],
      sblxList: [],
      locationList: [],
      areaList: [],
      institutionList: [],
      computerList: [],
      cabinetList: [],
      location: "",
      area: "",
      institution: "",
      jgId: "",
      dialogTitle: "",
      gdlx: "",
      qyRules: {
        relocationLocation: [
          { required: true, message: "请选择迁移所在地", trigger: "change" }
        ],
        relocationArea: [
          { required: true, message: "请选择迁移所在区县", trigger: "change" }
        ],
        relocationInstitution: [
          { required: true, message: "请选择迁移所在机构", trigger: "change" }
        ],
        relocationComputerRoomId: [
          { required: true, message: "请选择迁移场所", trigger: "change" }
        ],
        relocationCabinetId: [
          { required: true, message: "请选择迁移机柜", trigger: "change" }
        ],
        relocationTime: [
          { required: true, message: "请选择迁移时间", trigger: "change" }
        ],
        destroyTime: [
          { required: true, message: "请选择销毁截至时间", trigger: "change" }
        ],
        maintenanceTime: [
          {
            required: true,
            message: "请选择故障处理截至时间",
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {},
  mounted() {
    this.getComputerRoomList();
    this.getMaintenanceStatus();
    this.tjType = this.$route.query.type;
    if (this.tjType == "1") {
    } else if (this.tjType == "2") {
      this.id = this.$route.query.id;
      this.findEquipmentById();
    } else if (this.tjType == "3") {
      this.disabled = true;
      this.id = this.$route.query.id;
      this.findEquipmentById();
    }
  },
  methods: {
    async selectEquipmentInspectionProcessPage() {
      let data = await selectEquipmentInspectionProcessPage({
        id: this.form.id,
        pageNo: this.page,
        pageSize: this.pageSize
      });
      console.log(data);
      this.xjList = data.data.records;
      this.total = data.data.total;
    },
    async selectDestroyEquipmentTrajectoryPage() {
      let data = await selectDestroyEquipmentTrajectoryPage({
        equipmentCode: this.form.equipmentCode,
        page: this.page,
        pageSize: this.pageSize
      });
      console.log(data);
      this.gjxxList = data.data.records;
      this.total = data.data.total;
    },
    async selectEquipmentMaintenanceProcessPage() {
      let data = await selectEquipmentMaintenanceProcessPage({
        equipmentCode: this.form.equipmentCode,
        page: this.page,
        pageSize: this.pageSize
      });
      console.log(data);
      this.gzclList = data.data.records;
      this.total = data.data.total;
    },
    async getCity() {
      let data = await getCity();
      console.log(data);
      this.locationList = data.data;
    },
    locationChange(val) {
      console.log(val);
      this.location = val;
      this.formqy.relocationArea = "";
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getArea(val);
    },
    async getArea(val) {
      let data = await getArea({ citycode: val });
      console.log(data);
      this.areaList = data.data;
    },
    areaChange(val) {
      console.log(val);
      this.area = val;
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getDwxx(val);
    },
    async getDwxx(val) {
      let data = await getDwxx({ area: val });
      console.log(data);
      this.institutionList = data.data;
    },
    institutionChange(val) {
      console.log(val);
      this.institution = val;
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.getComputerList(val);
    },
    async getComputerList(val) {
      let data = await getComputerRoomList({
        institution: val
      });
      console.log(data);
      this.computerList = data.data;
    },
    computerChange(val) {
      console.log(val);
      this.jgId = val;
      this.formqy.relocationCabinetId = "";
      this.getCabinetList1(val);
    },
    async getCabinetList1(val) {
      let data = await getCabinetList({
        computerRoomId: val
      });
      console.log(data);
      this.cabinetList = data.data;
    },
    async getMaintenanceStatus() {
      let data = await getMaintenanceStatus();
      // this.wxztList = data.data;
      data.data.forEach(item => {
        if (item.equipment_main_type == 1) {
          this.wxztList.push(item);
        }
      });
      console.log(this.wxztList);
    },
    //生成二维码方法
    makeCode(item) {
      const result = new AraleQRCode({
        render: "svg", // 定义生成的类型 'svg' or 'table dom’
        text: item, // 二维码的链接
        size: 150 //二维码大小
      });

      // 将svg xml文档转换成字符串
      const svgXml = new XMLSerializer().serializeToString(result);

      // 将svg字符串转成base64格式，通过 window.btoa方法创建一个 base-64 编码的字符串，进行二次编码解码(encodeURIComponent 字符串进行编码和解码，unescape 进行解码)。
      const src =
        "data:image/svg+xml;base64," +
        window.btoa(unescape(encodeURIComponent(svgXml)));

      // 本地存储图片
      localStorage.setItem("image", src);
      this.getImg();
    },

    // 获取存储的图片给到页面
    getImg() {
      this.img = localStorage.getItem("image");
      console.log(this.img);

      localStorage.removeItem("image");
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    async print(val, formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          if (val == 1) {
            this.dialogVisible = true;
            this.dialogTitle = "设备迁移工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await migrateEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取迁移工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 2) {
            this.dialogVisible = true;
            this.dialogTitle = "设备销毁工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await destructionEquipment(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取销毁工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          } else if (val == 3) {
            this.dialogVisible = true;
            this.dialogTitle = "设备故障处理工单";
            let params = this.formqy;
            params.equipmentCode = this.form.equipmentCode;
            let data = await faultHandling(params);
            console.log(data);
            if (data.code == 10000) {
              this.xjgddyQbj = data.data; // 将迁移工单信息存储到全局变量
              this.makeCode(data.data.scanCode); // 生成二维码
              this.$message.success("成功获取故障处理工单信息!");
              this.qydialogVisible = false;
            } else {
              this.$message.error("提交失败");
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    removeItem() {
      this.formqy.relocationLocation = "";
      this.formqy.relocationArea = "";
      this.formqy.relocationInstitution = "";
      this.formqy.relocationComputerRoomId = "";
      this.formqy.relocationCabinetId = "";
      this.qydialogVisible = false;
    },
    async printToPDF() {
      if (this.gdlx == 1) {
        let data = await downloadMigrateEquipment(this.xjgddyQbj);
        this.dom_download(data, "设备迁移工单.docx");
      } else if (this.gdlx == 2) {
        let data = await downloadDestructionEquipment(this.xjgddyQbj);
        this.dom_download(data, "设备销毁工单.docx");
      } else if (this.gdlx == 3) {
        let data = await downloadFaultHandling(this.xjgddyQbj);
        this.dom_download(data, "设备故障处理工单.docx");
      }
      // this.isPrinting = true; // 设置打印状态
      // setTimeout(() => {
      //   const element = this.$refs.dialog.$el;
      //   html2canvas(element).then((canvas) => {
      //     const imgData = canvas.toDataURL("image/png");
      //     const pdf = new jsPDF("p", "mm", "a4"); // 使用A4纸张，可以调整为其他尺寸
      //     const imgProps = pdf.getImageProperties(imgData);
      //     const pdfWidth = pdf.internal.pageSize.getWidth();
      //     const pdfHeight = pdf.internal.pageSize.getHeight();

      //     // 设置图片宽度为PDF宽度的2倍
      //     const imgWidth = pdfWidth * 2;
      //     // 计算图片高度以保持比例
      //     const imgHeight = (imgProps.height / imgProps.width) * imgWidth;

      //     // 添加图片到PDF，居中对齐
      //     const imgX = (pdfWidth - imgWidth) / 2;
      //     const imgY = 10; // 设置图片的Y位置

      //     pdf.addImage(imgData, "PNG", imgX, imgY, imgWidth, imgHeight);
      //     pdf.save("场所巡检工单.pdf");
      //     this.isPrinting = false; // 重置打印状态
      //     this.dialogVisible = false; // 关闭弹框
      //   });
      // }, 1000); // 打印超时时间
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    async findEquipmentById() {
      let data = await findEquipmentById({ id: this.id });
      console.log(data, "data191");
      this.form = data.data;
      this.getEquipmentType();
    },
    // 获取机柜编号
    async getCabinetCode(computerRoomCode, id) {
      try {
        let params = { computerRoomCode, id };
        const res = await getCabinetCode(params);
        if (res.code === 10000) {
          this.form.cabinetCode = res.data.cabinetCode;
        } else {
          this.$message.error("获取机柜编号失败：" + res.message);
        }
      } catch (error) {
        console.error("获取机柜编号失败:", error);
        this.$message.error("获取机柜编号失败：" + error.message);
      }
    },
    async add() {
      if (this.form.equipmentMainType == 4) {
        let params = this.form;
        try {
          let data = await insertCabinet(params);
          if (data.code == 10000) {
            this.$message.success("提交成功");
            this.$router.push({ path: "/sbgl" });
          } else {
            this.$message.error("提交失败：" + data.message);
          }
        } catch (error) {
          console.error("提交失败:", error);
          this.$message.error("提交失败：" + error.message);
        }
      } else {
        let params = this.form;
        let data = await insertEquipment(params);
        console.log(data);
        if (data.code == 10000) {
          this.$message.success("提交成功");
          this.$router.push({ path: "/sbgl" });
        } else {
          this.$message.error("提交失败");
        }
      }
    },
    async update() {
      let params = this.form;
      let data = await updateEquipment(params);
      console.log(data);
      if (data.code == 10000) {
        this.$message.success("提交成功");
        this.$router.push({ path: "/sbgl" });
      } else {
        this.$message.error("提交失败");
      }
    },
    qygddy(val) {
      if (val == 1 && this.form.moveStatus == 1) {
        this.$message.error("设备正在迁移，无法进行迁移工单打印");
        return;
      }
      if (val == 2 && this.form.destroyStatus == 1) {
        this.$message.error("设备正在销毁，无法进行销毁工单打印");
        return;
      }
      if (val == 3 && this.form.maintenanceStatus == 1) {
        this.$message.error("设备正在维修，无法进行维修工单打印");
        return;
      }
      this.gdlx = val;
      this.getCity();
      this.qydialogVisible = true;
    },
    async getEquipmentType() {
      let data = await getEquipmentType({
        equipmentMainType: this.form.equipmentMainType
      });
      this.sblxList = data.data;
    },
    async sbzylxChange(val) {
      this.form.equipmentType = null;
      this.form.computerRoomId = null;
      this.form.computerRoomCode = null;
      this.form.computerRoomName = null;
      this.form.cabinetId = null;
      this.form.cabinetCode = null;
      this.form.cabinetName = null;
      this.form.equipmentCode = null;
      let data = await getEquipmentType({
        equipmentMainType: val
      });
      this.sblxList = data.data;
      let params = null;
      if (this.form.equipmentMainType == 1) {
        params = {
          computerRoomId: this.selectedItem.id,
          cabinetId: this.form.cabinetId,
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      } else if (this.form.equipmentMainType == 2) {
        params = {
          computerRoomId: this.selectedItem.id,
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      } else if (this.form.equipmentMainType == 3) {
        params = {
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      }
    },
    fh() {
      this.$router.push({ path: "/sbgl" });
    },
    //获取场所列表
    async getComputerRoomList() {
      const res = await getComputerRoomList();
      this.jfList = res.data;
    },
    async jfbhChange(val) {
      console.log(val);
      this.selectedItem = val; // 确保 selectedItem 被正确赋值
      this.getCabinetList();
      if (val) {
        this.form.computerRoomId = val.id; // 假设场所对象中有 id 字段
        this.form.computerRoomCode = val.computerRoomCode;
        this.form.computerRoomName = val.computerRoomName;
        // 其他字段的赋值...
        if (this.form.equipmentMainType == 4) {
          this.getCabinetCode(val.computerRoomCode, val.id);
        }
      } else {
        this.form.computerRoomId = "";
        this.form.computerRoomCode = "";
        this.form.computerRoomName = "";
        this.selectedItem = null;
      }
      let params = null;
      if (this.form.equipmentMainType == 1) {
        params = {
          computerRoomId: this.selectedItem.id,
          cabinetId: this.form.cabinetId,
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      } else if (this.form.equipmentMainType == 2) {
        params = {
          computerRoomId: this.selectedItem.id,
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      } else if (this.form.equipmentMainType == 3) {
        params = {
          equipmentMainType: this.form.equipmentMainType
        };
        const res = await getEquipmentCode(params);
        console.log(res);
        this.form.equipmentCode = res.data;
      }
    },
    // async getEquipmentCode() {
    //   let params = {
    //     computerRoomId: this.selectedItem.id,
    //     cabinetId: this.form.cabinetId,
    //     equipmentMainType: this.form.equipmentMainType,
    //   };
    //   const res = await getEquipmentCode(params);
    //   console.log(res);
    //   this.form.equipmentCode = res.data;
    // },
    //获取机柜列表
    async getCabinetList() {
      let params = {
        computerRoomId: this.selectedItem.id
      };
      const res = await getCabinetList(params);
      console.log(res);
      let data = res.data;
      this.jgList = data;
    },
    async jgbhChange(val) {
      console.log(val);
      if (val) {
        this.form.cabinetId = val.id; // 假设机柜对象中有 id 字段
        this.form.cabinetCode = val.cabinet_code;
        this.form.cabinetName = val.cabinet_name;
        let params = null;
        if (this.form.equipmentMainType == 1) {
          params = {
            computerRoomId: this.selectedItem.id,
            cabinetId: this.form.cabinetId,
            equipmentMainType: this.form.equipmentMainType
          };
          const res = await getEquipmentCode(params);
          console.log(res);
          this.form.equipmentCode = res.data;
        } else if (this.form.equipmentMainType == 2) {
          params = {
            computerRoomId: this.selectedItem.id,
            equipmentMainType: this.form.equipmentMainType
          };
          const res = await getEquipmentCode(params);
          console.log(res);
          this.form.equipmentCode = res.data;
        } else if (this.form.equipmentMainType == 3) {
          params = {
            equipmentMainType: this.form.equipmentMainType
          };
          const res = await getEquipmentCode(params);
          console.log(res);
          this.form.equipmentCode = res.data;
        }
      } else {
        this.form.cabinetId = "";
        this.form.cabinet_code = "";
        this.form.cabinet_name = "";
      }
    },
    sbxxClick(index) {
      console.log(index);
      this.sbxxQhVal = index;
      if (this.sbxxQhVal == 2) {
        this.selectDestroyEquipmentTrajectoryPage();
      } else if (this.sbxxQhVal == 3) {
        this.selectEquipmentMaintenanceProcessPage();
      } else if (this.sbxxQhVal == 4) {
        this.selectEquipmentInspectionProcessPage();
      }
    },

    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      if (this.sbxxQhVal == 2) {
        this.selectDestroyEquipmentTrajectoryPage();
      } else if (this.sbxxQhVal == 3) {
        this.selectEquipmentMaintenanceProcessPage();
      }
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      if (this.sbxxQhVal == 2) {
        this.selectDestroyEquipmentTrajectoryPage();
      } else if (this.sbxxQhVal == 3) {
        this.selectEquipmentMaintenanceProcessPage();
      }
    }
  },
  watch: {}
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
  padding-left: 10px;
}
.flex {
  width: 950px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-textarea .el-textarea__inner {
  width: 830px !important;
  height: 129px !important;
  border-radius: 2px;
}
.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 115px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}
.table ::-webkit-scrollbar:horizontal {
  display: block !important;
}
/deep/.el-table--scrollable-x .el-table__body-wrapper {
  height: 525px !important;
}
</style>
