import { createAP<PERSON>, createFile<PERSON><PERSON>, createUpload<PERSON>I, createDown, BASE_URL, createDownloadAPI } from './request'


export const getWarnChartsDatas = data => createAPI(BASE_URL + "/largeScreen/getEquipmentCountByType", 'get', data)
export const getAlarmTrendChartsDatas = data => createAPI(BASE_URL + "/largeScreen/getWarnLogCount", 'get', data)
export const getMapChartsDatas = data => createAPI(BASE_URL + "/largeScreen/getDlry", 'get', data)
export const getLockAlarmChartsDatas = data => createAPI(BASE_URL + "/largeScreen/getNewLog", 'get', data)