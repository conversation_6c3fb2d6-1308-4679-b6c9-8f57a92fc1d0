<template>
  <div class="out">
    <div
      class="card"
      @mouseleave="userCardMouseLeave"
      style="
        cursor: unset;
        flex: none;
        width: 120px;
        justify-content: start;
        position: relative;
      "
    >
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <!-- <i class="el-icon-minus" style="color: #FEC13F;"></i> -->
      <!-- <img src="../../assets/icons/zuxiaohua.png" /> -->
      <i
        @click="showUserCardFlag = !showUserCardFlag"
        class="el-icon-s-custom"
        style="
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          padding: 5px;
          box-sizing: border-box;
          font-size: 17px;
        "
      ></i>
      <div v-show="showUserCardFlag" class="div-user-card">
        <div style="border-bottom: 1px solid #e4e7ed">
          <!-- <i class="el-icon-caret-top"></i> -->
          <div class="pointer"></div>
          <span>{{ currentYhm }}</span>
        </div>
      </div>
      <!-- <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span> -->
      <!-- <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <span style="font-size: 13px;font-style: italic;color: #e8e8e8;">{{currentXm}}</span>
        </div>
        <i slot="reference" class="el-icon-s-custom" style="background: rgba(255,255,255,0.3);border-radius: 50%;padding: 5px;box-sizing: border-box;font-size: 17px;"></i>
      </el-popover> -->
    </div>
    <div class="card" @click="minimizeSystem()">
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <!-- <i class="el-icon-minus"></i> -->
      <!-- <img src="../../assets/icons/zuxiaohua.png" title="最小化" /> -->
      <!-- <span>最小化</span> -->
    </div>
    <div class="card" @click="maximizeSystem()">
      <!-- <i v-show="!windowBtnConfig.isMax" class="el-icon-full-screen"></i>
      <i v-show="windowBtnConfig.isMax" class="el-icon-copy-document"></i> -->
      <!-- <img v-show="!windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="最大化" />
      <img v-show="windowBtnConfig.isMax" src="../../assets/icons/zuxiaohua.png" title="窗口化" /> -->
      <!-- <span v-show="!windowBtnConfig.isMax">最大化</span>
      <span v-show="windowBtnConfig.isMax">窗口化</span> -->
    </div>
    <div class="card">
      <!-- <img src="../../assets/icons/header_icon8.png" /> -->
      <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <!-- <div v-for="(item,index) in systemSettingList" v-if="(item.showRoles.indexOf('all') != -1)||(item.showRoles.indexOf(currentYhm) != -1)" :key="index" @click="toPath(item.path)"><i :class="item.icon"></i>{{item.name}}</div> -->
          <!-- v-if="(item.showRoles.indexOf('all') != -1) || (currentYhlx == 1 ? item.showRoles.indexOf(currentYhm) != -1 : currentYhlx == 0 ? true : item.showYhlxs.indexOf(3) != -1)" -->
          <div
            v-for="(item, index) in systemSettingList"
            v-if="item.showYhlxs == 1"
            :key="index"
            @click="toPath(item.path)"
          >
            <i :class="item.icon"></i>{{ item.name }}
          </div>
        </div>
        <!-- <img slot="reference" src="../../assets/icons/header_icon8.png" style="margin-top: 5px;" /> -->
        <!-- <i slot="reference" class="el-icon-setting" style="color: #909399;"></i> -->
        <!-- <i slot="reference" class="el-icon-setting"></i> -->
        <i slot="reference" class="el-icon-s-operation"></i>
        <!-- <div slot="reference">
          <div style="display: flex;">
            <img src="../../assets/icons/shezhi.png" />
            <span>系统设置</span>
          </div>
        </div> -->
      </el-popover>
    </div>
    <!-- <div class="card" @click="quitSystem()"> -->
    <!-- <img src="../../assets/icons/header_icon9.png" /> -->
    <!-- <i class="el-icon-close" style="color: #FE645D;"></i> -->
    <!-- <i class="el-icon-close"></i> -->
    <!-- <img src="../../assets/icons/tuichu.png" title="退出系统" /> -->
    <!-- <span>退出</span> -->
    <!-- </div> -->
    <!--数据上报dialog-->
    <!-- <el-dialog title="数据上报" :visible.sync="dialogVisibleSjsb" width="35%">
      <div>
        <el-form :model="dialogSjsb" :label-position="'right'" label-width="120px" size="mini">
          <div style="display:flex">
            <el-form-item label="上报时间" class="one-line">
              <el-date-picker v-model="dialogSjsb.sbsj" type="year" value-format="yyyy" placeholder="选择上报时间"
                style="width:calc(100%);">
              </el-date-picker>
            </el-form-item>
          </div>
          <div style="display:flex">
            <el-form-item label="上报类型" class="one-line">
              <el-checkbox-group v-model="dialogSjsb.sblx">
                <el-checkbox :label="1"
                  :disabled="dialogSjsb.sblx.length == 1 && dialogSjsb.sblx.indexOf(1) != -1">涉密人员</el-checkbox>
                <el-checkbox :label="2"
                  :disabled="dialogSjsb.sblx.length == 1 && dialogSjsb.sblx.indexOf(2) != -1">定密事项</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="generateSbsj()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSjsb = false">关 闭</el-button>
      </span>
    </el-dialog> -->
    <!--数据上报dialog-->
    <el-dialog title="数据上报" :visible.sync="dialogVisibleSjsb" width="35%">
      <div>
        <el-form
          :model="dialogSjsb"
          :label-position="'right'"
          label-width="120px"
          size="mini"
        >
          <div style="display: flex">
            <el-form-item label="上报数据模式" class="one-line">
              <el-radio-group v-model="sbsjlx">
                <el-radio :label="1">本年数据模式</el-radio>
                <el-radio :label="2">自选数据模式</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="上报时间" class="one-line">
              <el-date-picker
                v-model="dialogSjsb.sbsj"
                type="year"
                value-format="yyyy"
                placeholder="选择上报时间"
                style="width: calc(100%)"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <div v-if="sbsjlx == 1" style="display: flex">
            <el-form-item label="上报类型" class="one-line">
              <el-checkbox-group v-model="dialogSjsb.sblx">
                <el-checkbox
                  :label="1"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(1) != -1
                  "
                  >涉密人员</el-checkbox
                >
                <el-checkbox
                  :label="2"
                  :disabled="
                    dialogSjsb.sblx.length == 1 &&
                    dialogSjsb.sblx.indexOf(2) != -1
                  "
                  >定密事项</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="generateSbsj()">保 存</el-button>
        <el-button type="warning" @click="dialogVisibleSjsb = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
    <!--数据导入dialog-->
    <el-dialog title="数据导入" :visible.sync="dialogVisibleSjdr" width="35%">
      <div style="width: 95%;border: 1px solid #e4e7ed;padding: 10px;display: flex; justify-content: flex-end;">
        <el-upload :http-request="uploadFile" action="/" :data="{}" class="upload-button" :show-file-list="false" :accept='accept' style="display: inline-block;">
                  <el-button size="small" type="primary">上传导入</el-button>
                </el-upload>
        <!-- <el-form
          :model="dialogSjdr"
          :label-position="'right'"
          label-width="120px"
          size="mini"
        >
          <el-form-item>
            <div style="width: 90%; height: 50px;">
              <div style="display: flex; justify-content: flex-end; align-items: center; height: 100%;">
                <a href="javascript:void(0);" @click="fileUrl" class="btn btn-primary btn-circle btn-icon-right btn-sm" style="width: 60%;">
                  {{ dialogSjdr.fileName}}</a>
               
              </div>
            </div>
          </el-form-item>
        </el-form> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="downloadImportTemplate()">下载导入模板</el-button>
        <el-button type="warning" @click="dialogVisibleSjdr = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!---->
    <el-dialog
      title="上报数据自选模式"
      :visible.sync="dialogVisibleSjsbZxms"
      width="90%"
    >
      <div>
        <el-tabs type="card" tab-position="left">
          <el-tab-pane label="涉密岗位" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.smgwList"
              @select="
                (selection, row) => tableSelected(selection, row, 'smgwList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'smgwList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="bmmc" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column
                prop="smdj"
                label="涉密等级"
                :formatter="forsmdj"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="涉密人员" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.smryList"
              @select="
                (selection, row) => tableSelected(selection, row, 'smryList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'smryList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="100%"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bmmc" label="部门"></el-table-column>
              <el-table-column prop="gwmc" label="岗位名称"></el-table-column>
              <el-table-column
                prop="smdj"
                label="涉密等级"
                :formatter="forsmdj"
              ></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column prop="sfsc" label="是否审查"></el-table-column>
              <el-table-column prop="sgsj" label="上岗时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="离岗离职" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.lzlgList"
              @select="
                (selection, row) => tableSelected(selection, row, 'lzlgList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'lzlgList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="bmmc" label="原部门"></el-table-column>
              <el-table-column prop="gwmc" label="原岗位名称"></el-table-column>
              <el-table-column
                prop="ysmdj"
                label="原涉密等级"
                :formatter="forsmdj"
              ></el-table-column>
              <el-table-column
                prop="tmqssj"
                label="脱密期开始时间"
              ></el-table-column>
              <el-table-column
                prop="tmjssj"
                label="脱密期结束时间"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密责任人" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.zrrList"
              @select="
                (selection, row) => tableSelected(selection, row, 'zrrList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'zrrList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="nd" label="年度"></el-table-column>
              <el-table-column prop="xm" label="姓名"></el-table-column>
              <el-table-column prop="zw" label="职务"></el-table-column>
              <el-table-column
                prop="dmqx"
                label="定密权限"
                :formatter="dmListdmqx"
              ></el-table-column>
              <el-table-column
                prop="dmsx"
                label="定密事项（范围）"
              ></el-table-column>
              <el-table-column
                prop="lb"
                label="类别"
                :formatter="dmListlb"
              ></el-table-column>
              <el-table-column
                prop="qdsj"
                label="确（指）定时间"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密授权" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmsqList"
              @select="
                (selection, row) => tableSelected(selection, row, 'dmsqList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmsqList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="sbnf" label="年度"></el-table-column>
              <el-table-column
                prop="bsqdwmc"
                label="被授权机关、单位名称"
              ></el-table-column>
              <el-table-column
                prop="sqdwmc"
                label="授权机关/单位名称"
              ></el-table-column>
              <!-- <el-table-column prop="sqqx" label="权限"  :formatter="dmListdmqx"></el-table-column> -->
              <el-table-column prop="sj" label="时间"></el-table-column>
              <el-table-column prop="qx" label="期限（年）"></el-table-column>
              <el-table-column prop="sx" label="事项"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="国家秘密事项" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.gjmmsxList"
              @select="
                (selection, row) => tableSelected(selection, row, 'gjmmsxList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'gjmmsxList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="sbnf"
                width="60"
                label="年度"
              ></el-table-column>
              <el-table-column
                prop="sx"
                width="140px"
                label="国家秘密事项名称"
              ></el-table-column>
              <el-table-column
                prop="mj"
                label="密级"
                :formatter="gjmmsxmjmc"
              ></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="zxfw" label="知悉范围"></el-table-column>
              <el-table-column prop="dmyj" label="定密依据"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密培训" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.dmpxList"
              @select="
                (selection, row) => tableSelected(selection, row, 'dmpxList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'dmpxList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="sbnf" label="年度"></el-table-column>
              <el-table-column prop="pxsj" label="培训时间"></el-table-column>
              <el-table-column
                prop="dmyj"
                label="学时（小时）"
              ></el-table-column>
              <el-table-column
                prop="pxrs"
                label="培训人数（人）"
              ></el-table-column>
              <el-table-column prop="pxdx" label="培训对象"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="定密情况年度统计" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.ndtjList"
              @select="
                (selection, row) => tableSelected(selection, row, 'ndtjList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'ndtjList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="tjnf"
                label="年度"
                width="60"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="gjmmzs"
                label="国家秘密数量"
              ></el-table-column>
              <el-table-column
                prop="dmzrrs"
                label="定密责任人数量"
              ></el-table-column>
              <el-table-column
                prop="dmsqxzs"
                label="定密授权数量"
              ></el-table-column>
              <el-table-column
                prop="gjmmylbs"
                label="国家秘密事项数量"
              ></el-table-column>
              <el-table-column
                prop="dmzds"
                label="定密制度数量"
              ></el-table-column>
              <el-table-column
                prop="dmpxcs"
                label="定密培训次数"
              ></el-table-column>
              <el-table-column
                prop="gzmms"
                label="工作秘密数量"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="不明确事项确定情况" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.bmqsxqdqkList"
              @select="
                (selection, row) =>
                  tableSelected(selection, row, 'bmqsxqdqkList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'bmqsxqdqkList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="sxmc" label="事项名称"></el-table-column>
              <el-table-column
                prop="mj"
                label="密级"
                :formatter="gjmmsxmjmc"
              ></el-table-column>
              <el-table-column prop="bmqx" label="保密期限"></el-table-column>
              <el-table-column prop="qrsj" label="确认时间"></el-table-column>
              <el-table-column prop="qrly" label="确认理由"></el-table-column>
              <el-table-column prop="djsj" label="登记时间"></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="保密制度" style="height: 403px">
            <el-table
              :data="sbsjDataZxmsOld.bmzdList"
              @select="
                (selection, row) => tableSelected(selection, row, 'bmzdList')
              "
              @select-all="
                (selection) => tableSelectedAll(selection, 'bmzdList')
              "
              border
              :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
              style="width: 100%; border: 1px solid #ebeef5"
              height="calc(100%)"
              stripe
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                width="60"
                label="序号"
                align="center"
              ></el-table-column>
              <el-table-column prop="sbnf" label="年度"></el-table-column>
              <el-table-column prop="wjm" label="文件名"></el-table-column>
              <el-table-column prop="wh" label="文号/版本号"></el-table-column>
              <el-table-column prop="zdysx" label="颁发日期"></el-table-column>
              <el-table-column prop="ssrq" label="实施日期"></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisibleSjsbZxms = false"
          >自选数据确认</el-button
        >
        <el-button type="warning" @click="dialogVisibleSjsbZxms = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <!---->
    <!---->
  </div>
</template>

<script>
// import { removeWindowLocation, getWindowLocation } from '../../../utils/windowLocation'

// import { writeLoginLog, writeSystemOptionsLog } from '../../../utils/logUtils'

// import { errorProcessor } from '../../../utils/errorProcessor'

// import { getZczpPath } from '../../../utils/pathUtil'

// import { encryptAes, decryptAes } from '../../../utils/aesUtils'

// import { dateFormatChinese } from '../../../utils/moment'

// import { checkArr } from '../../../utils/utils'

import {
  getAllGwxx,
  getAllYhxx,
  getAllLzlg,
  getAllDmzrr,
  getAllDmsq,
  getAllGjmmsx,
  getAllDmpx,
  getAllNdtj,
  getAllBmqsxqdqk,
  getAllBmzd,
  exportZxData,
  exportDmsxData,
  exportZxDwRyData,
  exportYhxxData,
} from "../../../api/sjsb";
import { getAllSmdj } from "../../../api/xlxz";
import { getDmqx, getzrrlb, getmj } from "../../../api/index";
import { getUserInfo } from "../../../api/dwzc";

import store from "../../store";
import {downloadImportTemplate, uploadMessage} from "../../../api/shma";
import {createDownloadAPI} from "../../../api/request";
export default {
  data() {
    return {
      smdj: [],
      dmgjmj: [],
      dmqxlxxz: [],
      dmlbxz: [],
      // 上报数据自选模式dialog显隐
      dialogVisibleSjsbZxms: false,
      // 上报数据自选模式(备选数据)
      sbsjDataZxmsOld: {
        smgwList: [],
        smryList: [],
        lzlgList: [],
        zrrList: [],
        dmsqList: [],
        gjmmsxList: [],
        dmpxList: [],
        ndtjList: [],
        bmqsxqdqkList: [],
        bmzdList: [],
      },
      // 上报数据(已选数据)
      sbsjDataZxms: {
        smgwList: [],
        smryList: [],
        lzlgList: [],
        zrrList: [],
        dmsqList: [],
        gjmmsxList: [],
        dmpxList: [],
        ndtjList: [],
        bmqsxqdqkList: [],
        bmzdList: [],
      },
      accept: '',//接受文件格式
      dialogSjdr: {
        fileName: "",
        fjmc: ""
      },
      // 数据上报数据类型默认值
      sbsjlx: 1,
      // 数据上报dialog数据
      dialogSjsb: {
        // 默认值
        sblx: [1, 2],
      },
      // 数据上报dialog显隐
      dialogVisibleSjsb: false,

      // 数据上报dialog显隐
      dialogVisibleSjdr: false,
      // 当前登录用户信息显隐
      showUserCardFlag: false,
      // 当前登录用户的姓名
      currentXm: "",
      // 当前登录用户的用户名(目前系统没有角色概念，故直接使用账号进行按钮显隐的判断)（用户名从登录时放入缓存的数据获取）
      currentYhm: "",
      // 当前登录用户的类型
      currentYhlx: "",
      /**
       * 最大化按钮控制配置
       * 窗口化时显示最大化
       * 最大化时显示窗口化
       */
      windowBtnConfig: {
        isMax: false,
      },
      // 系统设置菜单集合
      systemSettingList: [
        {
          name: "密码重置",
          path: "/mmczSetting",
          icon: "el-icon-edit",
          // 判断按钮是否需要隐藏（目前系统没有角色概念，故直接使用账号进行判断）
          showRoles: ["admin"],
          showYhlxs: 1,
        },
        {
          name: "修改密码",
          path: "/xgmmSetting",
          icon: "el-icon-edit",
          showRoles: ["all"],
          showYhlxs: 1,
        },
        // {
        //   name: "参数设置",
        //   path: "/systemSetting",
        //   icon: "el-icon-notebook-2",
        //   showRoles: ["admin", "xtAdmin"],
        //   showYhlxs: 1,
        // },
        // {
        //   name: '文件路径设置',
        //   path: '/filePathSetting',
        //   icon: 'el-icon-files',
        //   showRoles: ['admin', 'xtAdmin'],
        //   showYhlxs:1
        // },
        {
          name: "用户管理",
          path: "/yhglSetting",
          icon: "el-icon-files",
          showRoles: ["all"],
          showYhlxs: 1,
        },
        // {
        //   name: "检查报告上传",
        //   path: "/InspectionReportUpload",
        //   icon: "el-icon-bank-card",
        //   showRoles: ["admin", "xtAdmin"],
        //   showYhlxs: 1,
        // },
        // {
        //   name: "流程管理",
        //   path: "/lhglSetting",
        //   icon: "el-icon-files",
        //   showRoles: ["all"],
        //   showYhlxs: 1,
        // },
        {
          name: "注册登录信息",
          path: "/signIn",
          icon: "el-icon-circle-plus-outline",
          showRoles: ["admin", "xtAdmin"],
          showYhlxs: 1,
        },
        // {
        //   name: '代码管理',
        //   path: '/dmglSetting',
        //   icon: 'el-icon-c-scale-to-original',
        //   showRoles: ['admin', 'xtAdmin'],
        //   showYhlxs: [0, 1]
        // },
        {
          name: "数据导入",
          path: "/sjdrSetting",
          icon: "el-icon-bank-card",
          showRoles: ["admin", "xtAdmin"],
          showYhlxs: 1,
        },
        {
          name: "注册信息维护",
          path: "/zcxxSetting",
          icon: "el-icon-office-building",
          showRoles: ["admin", "xtAdmin"],
          showYhlxs: 1,
        },
        {
          name: "关于我们",
          path: "/gywmSetting",
          icon: "el-icon-message",
          showRoles: ["all"],
          showYhlxs: 1,
        },
        {
          name: "返回大屏",
          path: "/homePage",
          icon: "el-icon-data-board",
          showRoles: ["all"],
          showYhlxs: 1,
        },
        // {
        //   name: '工具箱',
        //   path: '/toolBox',
        //   icon: 'el-icon-message',
        //   showRoles: ['all']
        // },
        {
          name: "退出登录",
          path: "/quit",
          icon: "el-icon-warning-outline",
          showRoles: ["all"],
          showYhlxs: 1,
        },
      ],
    };
  },
  methods: {
    // 表格选择事件
    tableSelected(selection, row, tableName) {
      // console.log(selection, row, tableName);
      this.sbsjDataZxms[tableName] = selection;
    },
    // 表格全选事件
    tableSelectedAll(selection, tableName) {
      // console.log(selection, tableName);
      this.sbsjDataZxms[tableName] = selection;
    },
    async dwsj() {
      this.systemSettingList.forEach((item) => {
        item.showYhlxs = 1;
      });
      let data = await getUserInfo();
      this.currentYhm = data.yhm;
      if (data.yhlx == 2) {
        this.systemSettingList.forEach((item) => {
          if (
            item.name == "密码重置" ||
            item.name == "参数设置" ||
            item.name == "文件路径设置" ||
            item.name == "用户管理" ||
            item.name == "流程管理" ||
            item.name == "注册信息维护"
          ) {
            item.showYhlxs = 0;
          }
        });
      }
    },
    async fileUrl() {
    },
    async uploadFile (item) {
      const uploadData = new FormData();
      uploadData.append('file', item.file);
      const resHttp = await uploadMessage(uploadData);
      if (resHttp.code != 10000) {
        this.$message.error(resHttp.message);
        return;
      }
      else {
        this.$message.success('上传成功！');
        this.dialogVisibleSjdr = false;
      }
    },
    async downloadImportTemplate() {
      let returnData = await downloadImportTemplate();
      this.dom_download(returnData, "数据导入模板.xlsx");
    },
    // 初始化表格数据(自选模式)
    async initTableDataZxms() {
      //
      this.sbsjDataZxmsOld = {
        smgwList: [],
        smryList: [],
        lzlgList: [],
        zrrList: [],
        dmsqList: [],
        gjmmsxList: [],
        dmpxList: [],
        ndtjList: [],
        bmqsxqdqkList: [],
        bmzdList: [],
      };
      // 获取sm岗位信息（Smgwgl_list）(所有)
      this.sbsjDataZxmsOld.smgwList = await getAllGwxx();
      // 获取sm人员信息(smryList)(所有)
      this.sbsjDataZxmsOld.smryList = await getAllYhxx();
      // 获取离岗离职信息(lzlgList)(所有)
      this.sbsjDataZxmsOld.lzlgList = await getAllLzlg();
      // 获取dm责任人信息(zrrList)(所有)
      this.sbsjDataZxmsOld.zrrList = await getAllDmzrr();
      // console.log(this.sbsjDataZxmsOld.zrrList, "520");
      // 获取dm授权信息(dmsqList)(所有)
      this.sbsjDataZxmsOld.dmsqList = await getAllDmsq();
      // 获取国家mm事项信息(gjmmsxList)(所有)
      this.sbsjDataZxmsOld.gjmmsxList = await getAllGjmmsx();
      // 获取dm培训信息(dmpxList)(所有)
      this.sbsjDataZxmsOld.dmpxList = await getAllDmpx();
      // 获取dm情况年度统计信息(ndtjList)(所有)
      this.sbsjDataZxmsOld.ndtjList = await getAllNdtj();
      // 获取不明确事项确定情况信息(bmqsxqdqkList)(所有)
      this.sbsjDataZxmsOld.bmqsxqdqkList = await getAllBmqsxqdqk();
      // 获取zf采购项目情况信息(bmzdList)(所有)
      this.sbsjDataZxmsOld.bmzdList = await getAllBmzd();
    },
    // 当前登录用户信息鼠标移出事件
    userCardMouseLeave() {
      // console.log('鼠标移出去了')
      this.showUserCardFlag = false;
    },
    // 关闭 el-popover 弹出窗
    closeElPopover() {
      // console.log("closeElPopover mouseleave");
      this.elPopoverFlag = false;
    },
    // 页面跳转方法
    toPath(path) {
      if (path == "/quit") {
        // 写入登录日志
        // writeLoginLog(1)
        // 清除缓存
        // removeWindowLocation()
        // localStorage.removeItem('token');
        clearVuexAlong();
        store.commit("addNewToken", "");
        // sessionStorage.removeItem('token');
        // 注销后 清除session信息 ，并返回登录页
        // window.sessionStorage.removeItem('token');
        // this.common.startHacking(this, 'success', '注销成功！');
        localStorage.removeItem("dwmc");
        localStorage.removeItem("fs");
        localStorage.removeItem("bmid");
        localStorage.removeItem("dwjy");
        this.$router.push("/login");
        // state.token = null
        // this.$router.push('/')
        return;
      }
      else if (path == "/sjdrSetting") {
        // 设置上报数据dialog默认数据
        this.dialogVisibleSjdr = true;
        return;
      }
      this.$router.push(path);
    },
    // 数据上报方法
    async generateSbsj() {
      // console.log(this.sbsjlx);
      if (this.sbsjlx == 1) {
        let param = {
          sbnf: this.dialogSjsb.sbsj,
        };
        let returnData = await exportDmsxData(param);
        let date = new Date();
        let sj =
          date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate();
        this.dom_download(returnData, "定密数据上报表-" + sj + ".xlsx");
        let returnData1 = await exportYhxxData(param);
        this.dom_download(returnData1, "涉密数据上报表-" + sj + ".zip");
        this.dialogVisibleSjsb = false;
      } else if (this.sbsjlx == 2) {
        this.sbsjDataZxms.sbnf = this.dialogSjsb.sbsj;
        // console.log(this.dialogSjsb.sbsj, "629629629629629629");
        if (this.dialogSjsb.sbsj == "" || this.dialogSjsb.sbsj == undefined) {
          this.$message.warning("请选择上报时间！");
        } else {
          let param = this.sbsjDataZxms;
          // console.log(this.sbsjDataZxms);
          let returnData = await exportZxData(param);
          let date = new Date();
          let sj =
            date.getFullYear() +
            "" +
            (date.getMonth() + 1) +
            "" +
            date.getDate();
          this.dom_download(returnData, "定密自选数据表-" + sj + ".xlsx");
          let returnData1 = await exportZxDwRyData(param);
          this.dom_download(returnData1, "涉密自选数据表-" + sj + ".zip");
          this.dialogVisibleSjsb = false;
        }
      }
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },

    // 最小化系统
    minimizeSystem() {
      this.$electron.ipcRenderer.send("hide-window");
    },
    // 最大化系统
    maximizeSystem() {
      let isMax = this.windowBtnConfig.isMax;
      this.windowBtnConfig.isMax = !isMax;
      if (isMax) {
        // console.log("窗口化");
        this.$electron.ipcRenderer.send("unmax-window");
      } else {
        // console.log("最大化");
        this.$electron.ipcRenderer.send("max-window");
      }
    },
    // 退出系统
    quitSystem() {
      this.$confirm("是否执行此操作，点击将退出系统？", "是否退出系统？", {
        cancelButtonClass: "btn-custom-cancel",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true
      })
        .then(() => {
          clearVuexAlong();
          this.$emit("quitClicked", true);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消退出系统",
          });
        });
    },
    //涉密等级获取
    async smdjxz() {
      this.smdj = await getAllSmdj();
    },
    //获取定密事项定密权限
    async dmsxdmqx() {
      let data = await getDmqx();
      // console.log("获取定密事项定密权限:", data);
      this.dmqxlxxz = data;
    },
    async dmzzrlb() {
      let data = await getzrrlb();
      // console.log("获取定密责任人类别:", data);
      this.dmlbxz = data;
    },
    async dmmj() {
      let data = await getmj();
      this.dmgjmj = data;
    },
    forsmdj(row) {
      let hxsj;
      this.smdj.forEach((item) => {
        if (row.smdj == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },

    //列表数据回显
    dmListdmqx(row) {
      let listqx;
      this.dmqxlxxz.forEach((item) => {
        if (row.dmqx == item.id) {
          listqx = item.mc;
        }
      });
      return listqx;
    },
    dmListlb(row) {
      let listlb;
      this.dmlbxz.forEach((item) => {
        if (row.lb == item.id) {
          listlb = item.mc;
        }
      });
      return listlb;
    },
    gjmmsxmjmc(row) {
      let listqx;
      this.dmgjmj.forEach((item) => {
        if (row.mj == item.id) {
          listqx = item.mc;
        }
      });
      return listqx;
    },
  },
  mounted() {
    this.smdjxz();
    this.dmsxdmqx();
    this.dmzzrlb();
    this.dmmj();
    const PubSub = require("pubsub-js");
    PubSub.subscribe("data", (msg, data) => {
      this.currentYhm = data.yhm;
    });
    this.dwsj();
  },
  watch: {
    // 每次路由变动都去扫一遍缓存中的用户信息（为了处理账号切换时设置里的菜单未正确显隐问题）
    $route(to, form) {
      // console.log("路由变更，校验设置里的菜单");
      // console.log(to);
      // console.log(form);
      // 获取当前登录用户信息
      this.dwsj();
    },
    // 监听数据上报数据类型变动，如是往年数据，则需要弹框给用户提供选择
    sbsjlx(newVal, oldVal) {
      if (newVal == 1) {
        let now = new Date();
        this.dialogSjsb.sbsj = now.getFullYear() + "";
      } else {
        this.dialogSjsb.sbsj = undefined;
        this.initTableDataZxms();
        this.dialogVisibleSjsbZxms = true;
      }
    },
  },
};
</script>

<style scoped>
.out {
  display: flex;
  height: 100%;
  /* background: red; */
  color: white;
  height: 40px;
  /* background: rgba(255, 255, 255, 0.3); */
  background: url(../../assets/background/head-mall-box.png) no-repeat;
  background-size: cover;
  color: white;
  /* align-self: center; */
  width: 100%;
  /* border-radius: 18px; */
  padding: 0 10px;
}

.out .card {
  flex: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  /* flex-direction: column; */
  cursor: pointer;
}

/* .out .card:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.18);
} */
.out .card:nth-child(odd) {
  /* background: green; */
}

.out .card img {
  /* margin: 5px 10px 5px 0; */
  width: 22px;
  height: 22px;
  align-self: center;
  cursor: pointer;
  /* margin-right: 11.25px; */
  /* background: rgba(255, 255, 255, 0.5); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
}

.out .card img:hover {
}

.out .card span {
  font-size: 16px;
  font-weight: 400;
}

.out .card i {
  /* margin: 5px 10px 5px 0; */
  font-size: 20px;
  cursor: pointer;
  margin-right: 5px;
  /* background: rgba(255, 255, 255, 1); */
  /* padding: 5px; */
  /* box-sizing: border-box; */
  /* border-radius: 50%; */
  /* color: red; */
  /* font-weight: bold; */
}

.out .card i:hover {
}

/**系统设置菜单**/
.system-setting-out {
  /* background: red; */
  font-size: 13px;
}

.system-setting-out div {
  border-radius: 6px;
  margin: 3px 0;
  cursor: pointer;
  padding: 3px 0 3px 15px;
}

.system-setting-out div:hover {
  background: #f4f4f5;
  color: #409eff;
}

.system-setting-out > div i {
  margin-right: 10px;
}

/**当前登录用户信息展示卡片样式**/
.div-user-card {
  position: absolute;
  top: calc(27px + 10px);
  background: white;
  color: black;
  padding: 5px 10px;
  border-radius: 5px;
  /* width: 200px; */
  text-align: left;
}

.out .card .div-user-card span {
  font-size: 14px;
}

.out .card .div-user-card .pointer {
  /* background: #3dff00; */
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  /* border: 1px solid #909399; */
  background-image: linear-gradient(50deg, white, green);
}
</style>
