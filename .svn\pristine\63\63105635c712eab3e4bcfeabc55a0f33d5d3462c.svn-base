<template>
  <div id="container" class="container">
    <!-- v_代表着visualization 可视化  命名根据v_开头 -->
    <div class="v_header">
      <span class="v_name" @click="handleClick">进入系统</span>
      <!-- <img src="./img/newImages/enter.png" alt="" srcset=""> -->
    </div>
    <img class="v_bottom" src="./img/newImages/bottom.png" alt="" srcset="" />
    <!-- 省地图  根据用户信息匹配省份地图 -->
    <div class="v_chart_map">
      <Map :provinceName="provinceName" :mapData="chartDatas" :key="mapKey" />
    </div>
    <div class="v_chart_rightChart">
      <!-- 资产分布  环形图 -->
      <div class="v_chart_container">
        <div class="v_chart_header">
          <img src="./img/newImages/title1.png" alt="" srcset="" />
        </div>
        <div class="v_chart_body">
          <BingEchart :data="chartDatas1" />
        </div>
      </div>
      <!-- 告警趋势图  折现图 -->
      <div class="v_chart_container">
        <div class="v_chart_header">
          <img src="./img/newImages/title3.png" alt="" srcset="" />
        </div>
        <div class="v_chart_body">
          <LineCharts :data="chartDatas2" :markLineValue="75" markLineDate="09/27" />
        </div>
      </div>
      <!-- 锁控告警信息 -->
      <div class="v_chart_container">
        <div class="v_chart_header">
          <img src="./img/newImages/title2.png" alt="" srcset="" />
        </div>
        <div class="v_chart_body">
          <WarnCharts :alerts="chartDatas3" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BingEchart from "./components/bingCharts.vue";
import LineCharts from "./components/LineCharts.vue";
import WarnCharts from "./components/WarnCharts.vue";
import Map from "./components/Map.vue";

import {
  getWarnChartsDatas,
  getAlarmTrendChartsDatas,
  getMapChartsDatas,
  getLockAlarmChartsDatas,
} from "../../../api/v_page";
export default {
  data() {
    return {
      provinceName: "", // 当前省份名称
      mapKey: 0,
      chartDatas: [], // 地图
      chartDatas1: [], // 资产分布  环形图数据
      chartDatas2: [], // 告警趋势图  折线图
      chartDatas3: [], //锁控告警信息
    };
  },
  components: {
    BingEchart,
    LineCharts,
    WarnCharts,
    Map,
  },
  computed: {},
  created() {},
  mounted() {
    this.getMapChartsDatas();
    this.getWarnChartsDatas();
    this.getAlarmTrendChartsDatas();
    this.getLockAlarmChartsDatas();
  },
  methods: {
    // 获取资产分布
    async getWarnChartsDatas() {
      let res = await getWarnChartsDatas();
      if (res.code === 10000) {
        this.chartDatas1 = res.data;
      } else {
        this.$message.error(res.message);
      }
    },
    // 获取告警趋势图
    async getAlarmTrendChartsDatas() {
      let res = await getAlarmTrendChartsDatas();
      console.log(res, "92---");
      if (res.code === 10000) {
        this.chartDatas2 = res.data;
      } else {
        this.$message.error(res.message);
      }
    },
    // 获取地图数据
    async getMapChartsDatas() {
      let res = await getMapChartsDatas();
      if (res.code === 10000) {
        let arr1 = res.data.list;
        let arr2 = [];
        if (res.data.provinceName == "黑龙江") {
          arr2 = [
            { name: "哈尔滨市", value: [126.5358, 45.8038] },
            { name: "齐齐哈尔市", value: [123.9182, 47.3543] },
            { name: "牡丹江市", value: [129.6332, 44.5516] },
            { name: "佳木斯市", value: [130.3616, 46.8096] },
            { name: "大庆市", value: [125.1031, 46.5893] },
            { name: "鸡西市", value: [130.9418, 45.2952] },
            { name: "双鸭山市", value: [131.1591, 46.6469] },
            { name: "伊春市", value: [128.8993, 47.7248] },
            { name: "七台河市", value: [131.0031, 45.7717] },
            { name: "鹤岗市", value: [130.2979, 47.3499] },
            { name: "黑河市", value: [127.49902, 50.249584] },
            { name: "绥化市", value: [126.9891, 46.6348] },
            { name: "大兴安岭地区", value: [124.711525, 52.335262] },
          ];
        } else {
          arr2 = [
            { name: "乌鲁木齐市", value: [87.6168, 43.8256] },
            { name: "克拉玛依市", value: [84.8739, 45.5959] },
            { name: "吐鲁番市", value: [89.1849, 42.9479] },
            { name: "哈密市", value: [93.5142, 42.8183] },
            { name: "昌吉回族自治州", value: [87.3035, 44.011] },
            { name: "博尔塔拉蒙古自治州", value: [82.0661, 44.9056] },
            { name: "巴音郭楞蒙古自治州", value: [86.1507, 41.7642] },
            { name: "阿克苏地区", value: [80.2606, 41.1688] },
            { name: "克孜勒苏柯尔克孜自治州", value: [76.167, 39.7141] },
            { name: "喀什地区", value: [75.9892, 39.4704] },
            { name: "和田地区", value: [79.9253, 37.1107] },
            { name: "伊犁哈萨克自治州", value: [81.3169, 43.9178] },
            { name: "塔城地区", value: [82.98, 46.7454] },
            { name: "阿勒泰地区", value: [88.1388, 47.8449] },
          ];
        }
        const result = arr1.map((item) => {
          const found = arr2.find((city) => city.name === item.areaName);
          found.value[2] = item.count;
          found.value[3] = item.areaName;
          return {
            ...item,
            value: found ? found.value : null,
          };
        });
        this.chartDatas = result;
        this.provinceName = res.data.provinceName;
        this.mapKey++
      } else {
        this.$message.error(res.message);
      }
    },
    async getLockAlarmChartsDatas() {
      let res = await getLockAlarmChartsDatas();
      if (res.code === 10000) {
        this.chartDatas3 = res.data;
      } else {
        this.$message.error(res.message);
      }
    },
    handleClick() {
      const PubSub = require("pubsub-js");
      PubSub.publish("dataFh", "fh");
      this.$router.push("/ztqksy");
    },
  },
  watch: {},
};
</script>
<style scoped>
.container {
  width: 100%;
  height: 100%;
  background-image: url("./img/newImages/bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.container .v_header {
  width: calc(100vw * 1920 / 1920);
  height: calc(100vh * 100 / 1080);
  background-image: url("./img/newImages/head.png");
  background-repeat: no-repeat;
  background-size: contain;
}
.container .v_header .v_name {
  padding: 7px 30px 7px 30px;
  /* border-radius: 50px; */
  background-image: linear-gradient(
    90deg,
    rgba(0, 245, 255, 0.83) 0%,
    rgba(5, 86, 189, 0.48) 100%
  );
  border-radius: 139px;
  color: #fff;
  font-size: 16px;
  /* line-height: 30px; */
  float: right;
  margin-top: 20px;
  margin-right: 20px;
  cursor: pointer;
}
.v_bottom {
  position: absolute;
  bottom: 0px;
}
.v_chart_container {
  width: calc(100vw * 450 / 1920);
  height: calc(100vh * 272 / 1080);
  margin-top: calc(100vh * 35 / 1080);
}
.v_chart_header {
  height: calc(100vh * 32 / 1080);
  background-image: url("./img/newImages/title-bg.png");
  background-repeat: no-repeat;
  background-size: contain;
}
.v_chart_header img {
  height: calc(100vh * 30 / 1080);
  margin-left: 15px;
}
.v_chart_body {
  width: calc(100vw * 450 / 1920);
  height: calc(100vh * 240 / 1080);
  background-image: linear-gradient(
    270deg,
    rgba(2, 6, 30, 0) 0%,
    rgba(1, 46, 96, 0.83) 49%,
    rgba(1, 46, 96, 0) 100%
  );
  /* padding: 10px; */
}
.v_chart_map {
  width: calc(100vw * (1920 - 450 - 43) / 1920);
  height: calc(100vh * 980 / 1080);
  float: left;
}
.v_chart_rightChart {
  width: calc(100vw * 450 / 1920);
  /* height: calc(100vh * 1080 / 1080); */
  margin-right: calc(100vw * 43 / 1920);
  float: right;
}
</style>
