export default [{
        name: "jfgl",
        path: "/jfgl",
        component: () =>
            import ("../sbgl/jfgl.vue"),
        meta: {
            name: "场所管理",
            icon: "aaa",
            hidden: false,
            showHeaderMenu: true,
            showAsideMenu: true,
            requireAuth: true,
            menuList: [
                "/jfgl",
                "/jfglXqxg",
                "/jggl",
                "/sbgl",
                "/ccllgl",
                "/jfxjsjtj",
                "/sbqysjtj",
                "/sbxhsjtj",
                "/sbqysjtjXqy",
                "/sbxhsjtjXqy",
                "/jgglXqxg",
                "/sbglXqxg",
                "/ccllglXqxg",
                "/ccllgzcl",
                "/jfxjsjtjXqxg",
                "/gzclsjtj",
                "/gzclsjtjXqxg",
                "/sctxm",
                "/smzttz",
                "/smztzz",
                "/smztfz",
                "/smztjs",
                "/smztwf",
                "/smztqsdj",
                "/smztwcxd",
                "/smztjy",
                "/smztxh",
                "/xjczgl",
                "/xjczglXqy",
                "/qyczgl",
                "/qyczglXqy",
                "/zcbggl",
                "/zcbgglXqy",
                "/gzclgl",
                "/gzclglXqy",
                "/fhgcz",
                "/gzclglxxmx",
            ]
        }
    },
    {
        name: "gzclglxxmx",
        path: "/gzclglxxmx",
        component: () =>
            import ("../sbgl/gzclglxxmx.vue"),
        meta: {
            name: "故障处理信息明细",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "fhgcz",
        path: "/fhgcz",
        component: () =>
            import ("../sbgl/fhgcz.vue"),
        meta: {
            name: "非合规操作",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jfgl",
        path: "/jfgl",
        component: () =>
            import ("../sbgl/jfgl.vue"),
        meta: {
            name: "场所管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jfglXqxg",
        path: "/jfglXqxg",
        component: () =>
            import ("../sbgl/jfglXqxg.vue"),
        meta: {
            name: "场所管理信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jggl",
        path: "/jggl",
        component: () =>
            import ("../sbgl/jggl.vue"),
        meta: {
            name: "机柜管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jgglXqxg",
        path: "/jgglXqxg",
        component: () =>
            import ("../sbgl/jgglXqxg.vue"),
        meta: {
            name: "机柜管理信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "xjczgl",
        path: "/xjczgl",
        component: () =>
            import ("../sbgl/xjczgl.vue"),
        meta: {
            name: "巡检操作管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "xjczglXqy",
        path: "/xjczglXqy",
        component: () =>
            import ("../sbgl/xjczglXqy.vue"),
        meta: {
            name: "巡检操作信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "qyczgl",
        path: "/qyczgl",
        component: () =>
            import ("../sbgl/qyczgl.vue"),
        meta: {
            name: "迁移操作管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "qyczglXqy",
        path: "/qyczglXqy",
        component: () =>
            import ("../sbgl/qyczglXqy.vue"),
        meta: {
            name: "迁移操作信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "zcbggl",
        path: "/zcbggl",
        component: () =>
            import ("../sbgl/zcbggl.vue"),
        meta: {
            name: "资产变更管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "zcbgglXqy",
        path: "/zcbgglXqy",
        component: () =>
            import ("../sbgl/zcbgglXqy.vue"),
        meta: {
            name: "资产变更信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "gzclgl",
        path: "/gzclgl",
        component: () =>
            import ("../sbgl/gzclgl.vue"),
        meta: {
            name: "故障处理管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "gzclglXqy",
        path: "/gzclglXqy",
        component: () =>
            import ("../sbgl/gzclglXqy.vue"),
        meta: {
            name: "故障处理信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbgl",
        path: "/sbgl",
        component: () =>
            import ("../sbgl/sbgl.vue"),
        meta: {
            name: "资产管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbglXqxg",
        path: "/sbglXqxg",
        component: () =>
            import ("../sbgl/sbglXqxg.vue"),
        meta: {
            name: "资产管理信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "ccllgl",
        path: "/ccllgl",
        component: () =>
            import ("../sbgl/ccllgl.vue"),
        meta: {
            name: "长传链路管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "ccllglXqxg",
        path: "/ccllglXqxg",
        component: () =>
            import ("../sbgl/ccllglXqxg.vue"),
        meta: {
            name: "长传链路管理信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "ccllgzcl",
        path: "/ccllgzcl",
        component: () =>
            import ("../sbgl/ccllgzcl.vue"),
        meta: {
            name: "长传链路故障处理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jfxjsjtj",
        path: "/jfxjsjtj",
        component: () =>
            import ("../sbgl/jfxjsjtj.vue"),
        meta: {
            name: "巡检操作数据统计",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "jfxjsjtjXqxg",
        path: "/jfxjsjtjXqxg",
        component: () =>
            import ("../sbgl/jfxjsjtjXqxg.vue"),
        meta: {
            name: "巡检操作数据统计详情信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbqysjtj",
        path: "/sbqysjtj",
        component: () =>
            import ("../sbgl/sbqysjtj.vue"),
        meta: {
            name: "资产迁移数据统计",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbqysjtjXqy",
        path: "/sbqysjtjXqy",
        component: () =>
            import ("../sbgl/sbqysjtjXqy.vue"),
        meta: {
            name: "资产迁移数据统计详情信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbxhsjtj",
        path: "/sbxhsjtj",
        component: () =>
            import ("../sbgl/sbxhsjtj.vue"),
        meta: {
            name: "资产变更数据统计",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sbxhsjtjXqy",
        path: "/sbxhsjtjXqy",
        component: () =>
            import ("../sbgl/sbxhsjtjXqy.vue"),
        meta: {
            name: "资产变更数据统计详情信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "gzclsjtj",
        path: "/gzclsjtj",
        component: () =>
            import ("../sbgl/gzclsjtj.vue"),
        meta: {
            name: "故障处理数据统计",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "gzclsjtjXqxg",
        path: "/gzclsjtjXqxg",
        component: () =>
            import ("../sbgl/gzclsjtjXqxg.vue"),
        meta: {
            name: "故障处理数据统计详情信息",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smzttz",
        path: "/smzttz",
        component: () =>
            import ("../smzttz.vue"),
        meta: {
            name: "载体管理",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztzz",
        path: "/smztzz",
        component: () =>
            import ("../smztzz.vue"),
        meta: {
            name: "载体制作登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztfz",
        path: "/smztfz",
        component: () =>
            import ("../smztfz.vue"),
        meta: {
            name: "载体复制登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztjs",
        path: "/smztjs",
        component: () =>
            import ("../smztjs.vue"),
        meta: {
            name: "载体接收传递登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztwf",
        path: "/smztwf",
        component: () =>
            import ("../smztwf.vue"),
        meta: {
            name: "载体外发传递登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztqsdj",
        path: "/smztqsdj",
        component: () =>
            import ("../smztqsdj.vue"),
        meta: {
            name: "载体签收登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztwcxd",
        path: "/smztwcxd",
        component: () =>
            import ("../smztwcxd.vue"),
        meta: {
            name: "载体外出携带登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztjy",
        path: "/smztjy",
        component: () =>
            import ("../smztjy.vue"),
        meta: {
            name: "载体借阅登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "smztxh",
        path: "/smztxh",
        component: () =>
            import ("../smztxh.vue"),
        meta: {
            name: "载体销毁登记",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
    {
        name: "sctxm",
        path: "/sctxm",
        component: () =>
            import ("../sbgl/sctxm.vue"),
        meta: {
            name: "生成条形码",
            icon: "aaa",
            hidden: true,
            showHeaderMenu: true,
            requireAuth: true,
            showAsideMenu: true
        }
    },
];