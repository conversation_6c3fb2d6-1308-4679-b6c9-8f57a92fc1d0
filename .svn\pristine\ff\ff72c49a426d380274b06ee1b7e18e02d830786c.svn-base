<template>
  <div class="line-chart-container">
    <div ref="chartContainer" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "LineChart",
  props: {
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    data: {
      type: Array,
      default: () => [],
    },
    markLineValue: {
      type: Number,
      default: 75,
    },
    markLineDate: {
      type: String,
      default: "09/27",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  watch: {
    data: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer);
      this.updateChart();
    },
    updateChart() {
      if (!this.chart) return;

      const xAxisData = this.data.map((item) => item.name);
      const seriesData = this.data.map((item) => item.value);

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(26, 35, 50, 0.9)',
          borderColor: '#2d4a6b',
          borderWidth: 1,
          textStyle: {
            color: '#8bb5d1',
            fontSize: 12
          },
          formatter: params => {
            return `${params.name}: ${params.value}`;
          }
        },
        // backgroundColor: '#1a2332',
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: xAxisData,
          axisLine: {
            lineStyle: {
              color: "#2d4a6b",
            },
          },
          axisLabel: {
            color: "#8bb5d1",
            fontSize: 12,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          min: 0,
          max: Math.max(...this.data.map((item) => item.value)) + 10,
          // interval: 50,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#8bb5d1",
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: "#2d4a6b",
              width: 1,
            },
          },
        },
        series: [
          {
            type: "line",
            data: seriesData,
            lineStyle: {
              color: "#ff4757",
              width: 3,
            },
            itemStyle: {
              color: "#ff4757",
              borderColor: "#ff4757",
              borderWidth: 2,
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "rgba(240,50,50,1)",
                    },
                    {
                      offset: 1,
                      color: "rgba(240,50,50,0.1)",
                    },
                  ],
                  false
                ),
              },
            },
            symbol: "circle",
            symbolSize: 8,
            smooth: false,
            markLine: {
              silent: true,
              lineStyle: {
                color: "#ffd700",
                type: "dashed",
                width: 2,
              },
              label: {
                show: true,
                position: "middle",
                formatter: () => "",
                backgroundColor: "transparent",
              },
              data: [
                {
                  xAxis: this.data.reduce((prev, curr) =>
                    curr.value > prev.value ? curr : prev
                  ).name,
                  lineStyle: {
                    color: "#ffd700",
                    type: "dashed",
                    width: 2,
                  },
                },
              ],
            },
            markPoint: {
              symbol: "roundRect",
              symbolSize: [60, 30],
              itemStyle: {
                color: "#ffd700",
                borderColor: "#ffd700",
              },
              label: {
                show: true,
                color: "#1a2332",
                fontSize: 16,
                fontWeight: "bold",
              },
              data: [
                {
                  coord: [
                    this.data.reduce((prev, curr) =>
                      curr.value > prev.value ? curr : prev
                    ).name,
                    Math.max(...this.data.map((item) => item.value)) + 10,
                  ],
                  value: Math.max(...this.data.map((item) => item.value)),
                },
              ],
            },
          },
        ],
      };

      this.chart.setOption(option, true);
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
};
</script>

<style scoped>
.line-chart-container {
  width: 100%;
  height: 100%;
}
</style>
