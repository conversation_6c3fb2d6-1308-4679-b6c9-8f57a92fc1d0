import { createAPI, createFileAPI, createUploadAPI, createDown, BASE_URL, createDownloadAPI } from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

//管理端
//场所管理
//场所信息条件查询
export const jfQueryByCondition = data => createAPI(BASE_URL + "/computerRoomManagement/queryByCondition", 'get', data)
    //分页场所信息条件查询
export const queryComputerRoomPage = data => createAPI(BASE_URL + "/computerRoomManagement/queryComputerRoomPage", 'get', data)
    //巡检场所信息新增
export const getComputerRoomCode = data => createAPI(BASE_URL + "/computerRoomManagement/getComputerRoomCode", 'get', data)
    //场所管理新增
export const insertComputerRoom = data => createAPI(BASE_URL + "/computerRoomManagement/insertComputerRoom", 'post', data)
    //场所信息修改
export const updateComputerRoom = data => createAPI(BASE_URL + "/computerRoomManagement/updateComputerRoom", 'post', data)
    //场所信息删除
export const deleteComputerRoom = data => createAPI(BASE_URL + "/computerRoomManagement/deleteComputerRoom", 'post', data)
export const getEquipmentByComputerRoom = data => createAPI(BASE_URL + "/computerRoomManagement/getEquipmentByComputerRoom", 'get', data)
    //下载导入模板
export const downloadImportTemplate = data => createDownloadAPI(BASE_URL + "/computerRoomManagement/downloadImportTemplate", 'get', data)
    //初始文件导入
export const uploadMessage = data => createUploadAPI(BASE_URL + "/computerRoomManagement/uploadMessage", 'post', data)
    //导出文件
export const exportMessage = data => createAPI(BASE_URL + "/computerRoomManagement/exportMessage", 'post', data)
    //巡检场所工单打印
export const downloadInspectionForm = data => createAPI(BASE_URL + "/computerRoomManagement/downloadInspectionForm", 'post', data)
export const downloadInspectionComputerRoom = data => createDown(BASE_URL + "/computerRoomManagement/downloadInspectionComputerRoom", 'post', data)

export const downloadInspection = data => createDown(BASE_URL + "/computerRoomManagement/downloadInspection", 'post', data)
    //巡检场所结果信息上传
export const uploadInspectionForm = data => createUploadAPI(BASE_URL + "/computerRoomManagement/uploadInspectionForm", 'post', data)

//机柜管理
//机柜信息条件查询
export const jgQueryByCondition = data => createAPI(BASE_URL + "/cabinetManagement/queryByCondition", 'get', data)
    //机柜信息分页查询
export const queryCabinetPage = data => createAPI(BASE_URL + "/cabinetManagement/queryCabinetPage", 'get', data)
    //添加机柜时获取场所编号和名称
    // export const getComputerRoomList = data => createAPI(BASE_URL + "/computerRoomManagement/getComputerRoomList", 'get', data)
    //巡检场所信息新增
export const getCabinetCode = data => createAPI(BASE_URL + "/cabinetManagement/getCabinetCode", 'get', data)
    //机柜管理新增
export const insertCabinet = data => createAPI(BASE_URL + "/cabinetManagement/insertCabinet", 'post', data)
    //机柜信息修改
export const updateCabinet = data => createAPI(BASE_URL + "/cabinetManagement/updateCabinet", 'post', data)
    //机柜信息删除
export const deleteCabinet = data => createAPI(BASE_URL + "/cabinetManagement/deleteCabinet", 'post', data)

//设备管理
//设备信息条件查询
export const sbQueryByCondition = data => createAPI(BASE_URL + "/equipmentManagement/queryByCondition", 'get', data)
    //设备管理新增
export const insertEquipment = data => createAPI(BASE_URL + "/equipmentManagement/insertEquipment", 'post', data)
    //设备信息修改
export const updateEquipment = data => createAPI(BASE_URL + "/equipmentManagement/updateEquipment", 'post', data)
    //设备信息删除
export const deleteEquipment = data => createAPI(BASE_URL + "/equipmentManagement/deleteEquipment", 'post', data)
    //设备迁移工单打印
export const downloadMigrateEquipment = data => createDown(BASE_URL + "/computerRoomManagement/downloadMigrateMessage", 'post', data)
    //设备备份迁移工单打印
export const downloadMigrate = data => createDown(BASE_URL + "/computerRoomManagement/downloadMigrate", 'post', data)
    //设备迁移结果上传
export const uploadMigrateEquipment = data => createUploadAPI(BASE_URL + "/equipmentManagement/uploadMigrateEquipment", 'post', data)
    //设备销毁工单打印
export const downloadDestructionEquipment = data => createDown(BASE_URL + "/computerRoomManagement/downloadDestructionMessage", 'post', data)
    //设备备份销毁工单打印
export const downloadDestruction = data => createDown(BASE_URL + "/computerRoomManagement/downloadDestruction", 'post', data)
    //设备销毁结果上传
export const uploadDestructionEquipment = data => createUploadAPI(BASE_URL + "/equipmentManagement/uploadDestructionEquipment", 'post', data)
    //设备故障处理工单打印
export const downloadFaultHandlingEquipment = data => createDown(BASE_URL + "/computerRoomManagement/downloadFaultHandlingMessage", 'post', data)
    //设备备份故障处理工单打印
export const downloadFaultHandling = data => createDown(BASE_URL + "/computerRoomManagement/downFaultHandling", 'post', data)
    //设备故障处理结果上传
export const uploadFaultHandling = data => createUploadAPI(BASE_URL + "/equipmentManagement/uploadFaultHandling", 'post', data)
    //添加设备时获取场所编号和名称
export const getComputerRoomList = data => createAPI(BASE_URL + "/computerRoomManagement/getComputerRoomList", 'get', data)
    //添加设备时获取机柜编号和名称
export const getCabinetList = data => createAPI(BASE_URL + "/equipmentManagement/getCabinetList", 'get', data)
    //获取设备类型
export const getEquipmentCode = data => createAPI(BASE_URL + "/equipmentManagement/getEquipmentCode", 'get', data)
    //获取设备类型
export const getEquipmentType = data => createAPI(BASE_URL + "/equipmentManagement/getEquipmentType", 'get', data)
    //获取设备类型
export const findEquipmentById = data => createAPI(BASE_URL + "/equipmentManagement/findEquipmentById", 'get', data)
    //获取设备类型
export const getMaintenanceStatus = data => createAPI(BASE_URL + "/equipmentManagement/getMaintenanceStatus", 'get', data)
    //获取设备类型
export const getCity = data => createAPI(BASE_URL + "/equipmentManagement/getCity", 'get', data)
    //获取设备类型
export const getArea = data => createAPI(BASE_URL + "/equipmentManagement/getArea", 'get', data)
    //获取设备类型
export const getDwxx = data => createAPI(BASE_URL + "/equipmentManagement/getDwxx", 'get', data)
    //设备迁移
export const migrateEquipment = data => createAPI(BASE_URL + "/computerRoomManagement/migrateEquipment", 'post', data)
    //设备销毁
export const destructionEquipment = data => createAPI(BASE_URL + "/computerRoomManagement/destructionEquipment", 'post', data)
    //设备故障处理
export const faultHandling = data => createAPI(BASE_URL + "/computerRoomManagement/faultHandling", 'post', data)
    //设备销毁
export const selectDestroyEquipmentTrajectoryPage = data => createAPI(BASE_URL + "/equipmentManagement/selectDestroyEquipmentTrajectoryPage", 'get', data)
    //设备故障处理
export const selectEquipmentMaintenanceProcessPage = data => createAPI(BASE_URL + "/equipmentManagement/selectEquipmentMaintenanceProcessPage", 'get', data)
    //获取机柜温度
export const getCabinetTemerature = data => createAPI(BASE_URL + "/saveCabinetTh/getCabinetTemerature", 'get', data)
    //获取机柜湿度
export const getCabinetHumidity = data => createAPI(BASE_URL + "/saveCabinetTh/getCabinetHumidity", 'get', data)






//长链路管理
//长链路信息条件查询
export const cllQueryByCondition = data => createAPI(BASE_URL + "/linkFailureManagement/queryByCondition", 'get', data)
    //长链路管理新增
export const insertLinkFailure = data => createAPI(BASE_URL + "/linkFailureManagement/insertLinkFailure", 'post', data)
    //长链路信息修改
export const updateLinkFailure = data => createAPI(BASE_URL + "/linkFailureManagement/updateLinkFailure", 'post', data)
    //长链路信息删除
export const deleteLinkFailure = data => createAPI(BASE_URL + "/linkFailureManagement/deleteLinkFailure", 'post', data)
    //长链路维修工单打印
export const cllDownloadFaultHandling = data => createDown(BASE_URL + "/linkFailureManagement/downloadFaultHandling", 'post', data)
    //长链路故障处理结果上传
export const cllUploadFaultHandling = data => createUploadAPI(BASE_URL + "/linkFailureManagement/uploadFaultHandling", 'post', data)
    //长链路编号获取
export const ccllGetEquipmentCode = data => createAPI(BASE_URL + "/linkFailureManagement/getEquipmentCode", 'get', data)
    //长链路id查询
export const selectlinkFailureById = data => createAPI(BASE_URL + "/linkFailureManagement/selectlinkFailureById", 'get', data)
    //长链路处理过程
export const faultHandlingProcess = data => createAPI(BASE_URL + "/linkFailureManagement/faultHandlingProcess", 'post', data)
export const selectFaultHandlingProcess = data => createAPI(BASE_URL + "/linkFailureManagement/selectFaultHandlingProcess", 'get', data)

//大屏
//获取六种类型的具体数量
export const getEquipmentCount = data => createAPI(BASE_URL + "largeScreen/getEquipmentCount", 'get', data)
    //获取大屏List
export const getComputerRoomByCodeList = data => createAPI(BASE_URL + "largeScreen/getComputerRoomByCodeList", 'get', data)

export const selectComputerRoomInspectionProcessPage = data => createAPI(BASE_URL + "/inspectionComputerRoom/queryInspectionPage", 'get', data)

export const selectCabinetInspectionProcessPage = data => createAPI(BASE_URL + "/shmaInspectionCabinet/queryInspectionCabinet", 'get', data)

export const selectEquipmentInspectionProcessPage = data => createAPI(BASE_URL + "/shmaInspectionEquipment/queryInspectionEquipment", 'get', data)