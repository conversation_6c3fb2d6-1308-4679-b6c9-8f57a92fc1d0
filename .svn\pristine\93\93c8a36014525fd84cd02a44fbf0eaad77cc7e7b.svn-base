<template>
  <div ref="myChart" class="con" style=""></div>
</template>
 
<script>
// 引入提示框和title组件
// 引入基本模板
import * as echarts from 'echarts'
export default {
  props: {
    list: {
      type: Array
    },
    tabType: {
      type: String
    },
    data: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
    }
  },
  mounted() {
    this.drawLine();
  },
  watch: {
    data(newValue) {
      if (newValue) {
        this.drawLine();
      }
    }
  },
  methods: {
    drawLine() {
      //实例存在则消除
      let chart = echarts.getInstanceByDom(this.$refs.myChart)
      if (chart) { chart.dispose() }
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(this.$refs.myChart)
      let datas = this.data
      // console.log(datas, '44---')
      let list = [];
      const color = [
        new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#3295ff' },
          { offset: 1, color: '#56e4ff' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#56ffb6' },
          { offset: 1, color: '#56e4ff' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#ffd700' },
          { offset: 1, color: '#ffa500' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#ff6e76' },
          { offset: 1, color: '#ff9e7d' }
        ]),
        new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#a18cd1' },
          { offset: 1, color: '#fbc2eb' }
        ])
      ]
      let max = Math.max(...datas.map((el) => el.value));
      datas.forEach((el, i) => {
        list.push(
          {
            value: el.value,
            name: el.name,
            // selected: el.selected,
            label: {
              normal: {
                formatter: '{b|{b}}\n{hr|{c}}',
                rich: {
                  b: {
                    fontSize: 12,
                    color: '#B0D2FF',
                    align: 'left',
                    // padding: [30, 0, 0, 0],
                  },
                  hr: {
                    fontSize: 14,
                    color: '#FFFFFF',
 
                    padding: [5, 0, 0, 0]
                  },
                }
              }
            },
            itemStyle: {
              normal: {
                borderWidth: 0,
                color: color[i % color.length],
                shadowColor: 'rgba(0,0,0,0.3)',
                shadowBlur: 10,
                shadowOffsetY: 5
              },
              emphasis: {
                shadowColor: 'rgba(0,0,0,0.5)',
                shadowBlur: 20,
                shadowOffsetY: 10
              }
            },
          },
          {
            value: max * 0.03,
            name: '',
            
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0,
              },
            },
          }
        );
      })
      myChart.setOption({
        title: {
          text: '资产分布',
          left: '50%',
          top: '50%',
          textAlign: 'center',
          textVerticalAlign: 'middle',
          textStyle: {
            fontSize: 18,
            color: '#B0D2FF',
            letterSpacing: 0,
            fontWeight: 700
          }
        },
        backgroundColor: 'transparent',
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationDelayUpdate: function (idx) {
          return idx * 100;
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'rgba(50,149,255,0.8)',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        // },
        series: [{
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          selectedOffset: 15,
          selectedMode: 'single',
          hoverOffset: 10,
          avoidLabelOverlap: true,
          data: list,
          itemStyle: {
            emphasis: {
              shadowBlur: 20,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            normal: {
              show: true,
              length: 15,
              length2: 20,
              lineStyle: {
                color: 'rgba(50,149,255,0.6)',
                width: 1.5,
                type: 'dashed'
              },
              smooth: 0.2
            }
          },
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            return Math.random() * 200;
          }
        },
        ]
      });
      myChart.off("click"); 
      const that = this
    //   myChart.on('click', function (param) { 
    //     if(param.name != ''){
    //       that.$emit('alarmInfoPieClick',param.name)
    //     }
    //   });
 
    }
  }
}
</script>

<style scoped>
.con {
    width: calc(100% - 20px); 
    height: calc(100% - 20px); 
    padding: 10px;
    background-image: url('../img/newImages/huan.png');
    background-repeat: no-repeat;
    /* background-size: contain; */
    background-position-x: center;
    background-position-y: center;
}
</style>