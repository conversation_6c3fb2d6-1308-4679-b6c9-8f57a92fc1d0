<template>
  <div id="container" class="large">
    <!-- 在此界面开发保密工作整体情况可视化大屏，要求自适应，可仿照安全态势自适应写 -->
    <div class="con">
      <div class="dpTitle">
        <div class="dpTitleTime">{{ currentTime }}</div>
        <div class="dpTitleZtqk">思和机房整体情况</div>
        <div class="dpTitleFh" @click="fh">
          <img src="./img/用户.png" alt="" />
          <span class="dpTitleFhSpan">管理员</span>
          <img src="./img/进入.png" alt="" />
        </div>
      </div>
      <div class="dpLeft">
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">服务器情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ fwqObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fwqObj.usedCount.length > 2"
                  >{{ fwqObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fwqObj.usedCount.length > 1"
                  >{{ fwqObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fwqObj.usedCount.length > 0"
                  >{{ fwqObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fwqObj.unusedCount.length > 2"
                  >{{ fwqObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fwqObj.unusedCount.length > 1"
                  >{{ fwqObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fwqObj.unusedCount.length > 0"
                  >{{ fwqObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fwqObj.destroyCount.length > 2"
                  >{{ fwqObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fwqObj.destroyCount.length > 1"
                  >{{ fwqObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fwqObj.destroyCount.length > 0"
                  >{{ fwqObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">交换机情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ jhjObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="jhjObj.usedCount.length > 2"
                  >{{ jhjObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="jhjObj.usedCount.length > 1"
                  >{{ jhjObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="jhjObj.usedCount.length > 0"
                  >{{ jhjObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="jhjObj.unusedCount.length > 2"
                  >{{ jhjObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="jhjObj.unusedCount.length > 1"
                  >{{ jhjObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="jhjObj.unusedCount.length > 0"
                  >{{ jhjObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="jhjObj.destroyCount.length > 2"
                  >{{ jhjObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="jhjObj.destroyCount.length > 1"
                  >{{ jhjObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="jhjObj.destroyCount.length > 0"
                  >{{ jhjObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">路由器情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ lyqObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="lyqObj.usedCount.length > 2"
                  >{{ lyqObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="lyqObj.usedCount.length > 1"
                  >{{ lyqObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="lyqObj.usedCount.length > 0"
                  >{{ lyqObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="lyqObj.unusedCount.length > 2"
                  >{{ lyqObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="lyqObj.unusedCount.length > 1"
                  >{{ lyqObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="lyqObj.unusedCount.length > 0"
                  >{{ lyqObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="lyqObj.destroyCount.length > 2"
                  >{{ lyqObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="lyqObj.destroyCount.length > 1"
                  >{{ lyqObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="lyqObj.destroyCount.length > 0"
                  >{{ lyqObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
      </div>
      <div class="dpMap">
        <dt1 ref="dt" @cityCodeSelected="handleCityCodeSelected"></dt1>
      </div>
      <div class="dpInput">
        <el-input
          v-model="dwpmqk"
          class="dpInputM"
          placeholder="搜索关键字"
        ></el-input>
        <!-- <input type="text" placeholder="单位排名情况"> -->
        <div class="dpSsAn" @click="search">搜索</div>
      </div>
      <div class="dpBottom">
        <div class="dpTableBtn">
          <div class="dpTableTitle">
            <ul>
              <li>序号</li>
              <li>地市名称</li>
              <li>机房数量</li>
              <li>服务器</li>
              <li>交换机</li>
              <li>路由器</li>
              <li>专用设备</li>
              <li>波分设备</li>
              <li>分光器</li>
            </ul>
          </div>
          <div class="dpTableCon" v-if="this.dwData.length != 0">
            <div
              class="dpTableConMh"
              v-for="(item, index) in dwData"
              :key="index"
            >
              <span class="table-text">
                {{ index < 9 ? 0 : "" }}{{ index + 1 }}
              </span>
              <p class="tb-item" :title="item.name">
                {{ item.name }}
              </p>
              <p class="tb-item" :title="item.count">{{ item.count }}</p>
              <p class="tb-item" :title="item.server">{{ item.server }}</p>
              <p class="tb-item" :title="item.switch">{{ item.switch }}</p>
              <p class="tb-item" :title="item.router">{{ item.router }}</p>
              <p class="tb-item" :title="item.special">{{ item.special }}</p>
              <p class="tb-item" :title="item.wave">{{ item.wave }}</p>
              <p class="tb-item" :title="item.optical">{{ item.optical }}</p>
            </div>
          </div>
          <div v-if="this.dwData.length == 0" class="dpTableConZwsj">
            暂无数据
          </div>
        </div>
      </div>

      <!-- <div class="dpTableRight">
        <div class="dpTableRightTitle">
          <ul>
            <li>序号</li>
            <li>县区</li>
            <li>机房数量</li>
          </ul>
        </div>
        <div class="dpTableRightCon" v-if="this.xqData.length != 0">
          <div
            class="dpTableRightConMh"
            v-for="(item, index) in xqData"
            :key="index"
          >
            <span class="table-text1">
              {{ index < 9 ? 0 : "" }}{{ index + 1 }}
            </span>
            <p class="tb-item2" :title="item.name">{{ item.name }}</p>
            <p class="tb-item2" :title="item.count">{{ item.count }}</p>
          </div>
        </div>
        <div class="dpTableRightConZwsj" v-if="this.xqData.length == 0">
          暂无数据
        </div>
      </div> -->
      <div class="dpRight">
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">专用设备情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ zysbObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="zysbObj.usedCount.length > 2"
                  >{{ zysbObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="zysbObj.usedCount.length > 1"
                  >{{ zysbObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="zysbObj.usedCount.length > 0"
                  >{{ zysbObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="zysbObj.unusedCount.length > 2"
                  >{{ zysbObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="zysbObj.unusedCount.length > 1"
                  >{{ zysbObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="zysbObj.unusedCount.length > 0"
                  >{{ zysbObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="zysbObj.destroyCount.length > 2"
                  >{{ zysbObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="zysbObj.destroyCount.length > 1"
                  >{{ zysbObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="zysbObj.destroyCount.length > 0"
                  >{{ zysbObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">波分设备情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ bfsbObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="bfsbObj.usedCount.length > 2"
                  >{{ bfsbObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="bfsbObj.usedCount.length > 1"
                  >{{ bfsbObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="bfsbObj.usedCount.length > 0"
                  >{{ bfsbObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="bfsbObj.unusedCount.length > 2"
                  >{{ bfsbObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="bfsbObj.unusedCount.length > 1"
                  >{{ bfsbObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="bfsbObj.unusedCount.length > 0"
                  >{{ bfsbObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="bfsbObj.destroyCount.length > 2"
                  >{{ bfsbObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="bfsbObj.destroyCount.length > 1"
                  >{{ bfsbObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="bfsbObj.destroyCount.length > 0"
                  >{{ bfsbObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
        <div class="dpLeftSgMk">
          <div class="dpLeftSgMkTitle">分光器情况</div>
          <div class="dpLeftSgMkCon">
            <div class="dpLeftSgMkConZts">
              <div class="dpLeftSgMkConZtsTitle">
                <span class="dpLeftSgMkConZtsTitle1">{{ fgqObj.total }}</span>
                <span class="dpLeftSgMkConZtsTitle2">台</span>
              </div>
              <div class="dpLeftSgMkConZtsNum">总台数</div>
            </div>
            <div class="dpLeftSgMkConQk1">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fgqObj.usedCount.length > 2"
                  >{{ fgqObj.usedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fgqObj.usedCount.length > 1"
                  >{{ fgqObj.usedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz"
                  v-if="fgqObj.usedCount.length > 0"
                  >{{ fgqObj.usedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz" v-else>0</span>
              </div>
              <div class="dpLeftSgMkConQkWzFl">使用中</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk2">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fgqObj.unusedCount.length > 2"
                  >{{ fgqObj.unusedCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fgqObj.unusedCount.length > 1"
                  >{{ fgqObj.unusedCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1"
                  v-if="fgqObj.unusedCount.length > 0"
                  >{{ fgqObj.unusedCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz1" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl1">未使用</div>
            </div>
            <div class="dpLeftSgMkConQk1 dpLeftSgMkConQk3">
              <div class="dpLeftSgMkConQkSzk">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fgqObj.destroyCount.length > 2"
                  >{{ fgqObj.destroyCount[0] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk1">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fgqObj.destroyCount.length > 1"
                  >{{ fgqObj.destroyCount[1] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkSzk dpLeftSgMkConQkSzk2">
                <span
                  class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2"
                  v-if="fgqObj.destroyCount.length > 0"
                  >{{ fgqObj.destroyCount[2] }}</span
                >
                <span class="dpLeftSgMkConQkWz dpLeftSgMkConQkWz2" v-else
                  >0</span
                >
              </div>
              <div class="dpLeftSgMkConQkWzFl2">销毁</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dt1 from "./components/dt1.vue";
import bing from "./components/bing.vue";

import {
  getEquipmentCount,
  getComputerRoomByCodeList,
} from "../../../api/shma";
import store from "../../store";

import { dateFormatLs, dateFormat } from "@/utils/moment.js";
export default {
  data() {
    return {
      code: "",
      keyName: "",
      dwData: [],
      dwpmqk: "",
      currentTime: "",
      fwqObj: {},
      jhjObj: {},
      lyqObj: {},
      zysbObj: {},
      bfsbObj: {},
      fgqObj: {},
    };
  },
  components: {
    dt1,
    bing,
  },
  computed: {},
  created() {},
  mounted() {
    this.getEquipmentCount();
    this.getComputerRoomByCodeList();
    this.updateTime();
  },
  methods: {
    handleCityCodeSelected(cityCode, cityName, NumTf) {
      console.log(cityCode, NumTf, "==============");
      console.log("当前点击城市的code", cityCode);
      console.log("当前点击城市的name", cityName);
      // 判断NumTf为偶数
      if (NumTf % 2 == 0) {
        this.cityCode = "";
        this.keyName = "";
      } else {
        this.code = cityCode;
        this.keyName = cityName;
      }
      this.getEquipmentCount();
      this.getComputerRoomByCodeList();
    },
    async getEquipmentCount() {
      let params = {
        code: this.code,
        keyName: this.keyName,
      };
      let res = await getEquipmentCount(params);
      if (res.code == 10000) {
        this.fwqObj = res.data[0];
        this.jhjObj = res.data[1];
        this.lyqObj = res.data[2];
        this.zysbObj = res.data[3];
        this.bfsbObj = res.data[4];
        this.fgqObj = res.data[5];
      }
    },
    async getComputerRoomByCodeList() {
      let params = {
        code: this.code,
        keyName: this.keyName,
      };
      let res = await getComputerRoomByCodeList(params);
      if (res.code == 10000) {
        this.dwData = res.data;
      }
    },
    fh() {
      const PubSub = require("pubsub-js");
      PubSub.publish("dataFh", "fh");
      this.$router.push("/ztqksy");
    },
    search() {
      this.getComputerRoomByCodeList();
    },
    updateTime() {
      const now = new Date();
      this.currentTime = dateFormatLs(now);
    },
  },
  watch: {},
};
</script>
<style scoped>
.large {
  width: calc(100vw * 1920 / 1920);
  height: calc(100vh * 1080 / 1080);
  background: url(./img/bg.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  left: 0px;
  /* background-position-x: -55px; */
  top: 0;
}

.con {
  position: relative;
}

.dpTitle {
  width: calc(100vw * 1920 / 1920);
  height: calc(100vh * 0.093);
  background: url(./img/head.png) no-repeat center;
  background-size: 100% 100%;
}

.dpTitleTime {
  width: calc(100vw * 0.072);
  height: calc(100vh * 0.019);
  position: absolute;
  top: calc(100vh * 0.014);
  left: calc(100vw * 0.015);
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 400;
}

.dpTitleLogo {
  width: calc(100vw * 0.028);
  height: calc(100vh * 0.039);
  position: absolute;
  top: calc(100vh * 0.006);
  left: calc(100vw * 0.362);
  background: url(./img/baomibiaozhi.png) no-repeat center;
  background-size: 100% 100%;
}

.dpTitleZtqk {
  width: calc(100vw * 0.211);
  height: calc(100vh * 0.065);
  position: absolute;
  top: calc(100vh * 0.01);
  left: calc(100vw * 0.395);
  font-size: calc(100vw * 40 / 1920);
  /* color: #ffffff; */
  background: linear-gradient(
    to bottom,
    #ffffff 25%,
    #ccd8f2 50%,
    #3869cc 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-family: SourceHanSansSC-Bold;
  letter-spacing: calc(100vw * 3.4 / 1920);
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
}

.dpTitleDyfb {
  width: calc(100vw * 0.115);
  height: calc(100vh * 0.029);
  line-height: calc(100vh * 0.029);
  position: absolute;
  top: calc(100vh * 0.097);
  left: calc(100vw * 0.443);
  font-family: YouSheBiaoTiHei;
  font-size: calc(100vw * 24 / 1920);
  text-align: center;
  color: #02fdf8;
  letter-spacing: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

.dpTitleFh {
  width: calc(100vw * 0.072);
  height: calc(100vh * 0.019);
  position: absolute;
  top: calc(100vh * 0.014);
  right: calc(100vw * 0.015);
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.dpTitleFhSpan {
  margin-left: calc(100vw * 0.0035);
  margin-right: calc(100vw * 0.0025);
}
.dpLeft {
  width: calc(100vw * 0.26);
  height: calc(100vh * 0.911);
  position: absolute;
  top: calc(100vh * 0.083);
  left: calc(100vw * 0.01);
}
.dpLeftSgMk {
  width: calc(100vw * 0.26);
  height: calc(100vh * 0.301);
  background: url(./img/bg-box.png) no-repeat center;
  background-size: 100% 100%;
  position: relative;
}
.dpLeftSgMkTitle {
  width: calc(100vw * 0.225);
  height: calc(100vh * 0.043);
  position: absolute;
  top: calc(100vh * 0.005);
  left: calc(100vw * 0.018);
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 24 / 1920);
  background: linear-gradient(to bottom, #ffffff 40%, #4accff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: 0;
  font-weight: 700;
  display: flex;
  align-items: center;
  padding-left: calc(100vw * 0.008);
}
.dpLeftSgMkCon {
  width: calc(100vw * 0.218);
  height: calc(100vh * 0.183);
  position: absolute;
  top: calc(100vh * 0.071);
  left: calc(100vw * 0.027);
}
.dpLeftSgMkConZts {
  width: calc(100vw * 0.068);
  height: calc(100vh * 0.115);
  position: absolute;
  top: calc(100vh * 0.032);
  left: calc(100vw * 0.017);
}
.dpLeftSgMkConZtsTitle1 {
  display: inline-block;
  width: calc(100vw * 0.026);
  height: calc(100vh * 0.037);
  position: absolute;
  top: calc(100vh * 0.032);
  left: calc(100vw * 0.02);
  font-family: SourceHanSansCN-Bold;
  font-size: calc(100vw * 28 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
}
.dpLeftSgMkConZtsTitle2 {
  display: inline-block;
  width: calc(100vw * 0.007);
  height: calc(100vh * 0.013);
  line-height: calc(100vh * 0.005);
  position: absolute;
  top: calc(100vh * 0.05);
  left: calc(100vw * 0.047);
  font-family: SourceHanSansCN-Bold;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}
.dpLeftSgMkConZtsNum {
  width: calc(100vw * 0.025);
  height: calc(100vh * 0.022);
  line-height: calc(100vh * 0.005);
  position: absolute;
  top: calc(100vh * 0.072);
  left: calc(100vw * 0.021);
  font-family: SourceHanSansCN-Bold;
  font-size: calc(100vw * 16 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}
.dpLeftSgMkConQk1 {
  width: calc(100vw * 0.12);
  height: calc(100vh * 0.042);
  background-image: linear-gradient(
    -88deg,
    rgba(50, 62, 124, 0) 1%,
    rgba(0, 140, 255, 0.13) 38%,
    rgba(0, 140, 255, 0.18) 71%,
    rgba(0, 140, 255, 0) 100%
  );
  border-radius: 8px;
  position: absolute;
  top: calc(100vh * 0.011);
  left: calc(100vw * 0.098);
}
.dpLeftSgMkConQk2 {
  top: calc(100vh * 0.071);
  left: calc(100vw * 0.098);
}
.dpLeftSgMkConQk3 {
  top: calc(100vh * 0.131);
  left: calc(100vw * 0.098);
}
.dpLeftSgMkConQkSzk {
  width: calc(100vw * 0.013);
  height: calc(100vh * 0.029);
  position: absolute;
  top: calc(100vh * 0.006);
  left: calc(100vw * 0.02);
  background: url(./img/数字框.png) no-repeat center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dpLeftSgMkConQkWz {
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 26 / 1920);
  background: linear-gradient(to bottom, #ffffff 40%, #4accff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: 0;
  font-weight: 700;
}
.dpLeftSgMkConQkWz1 {
  background: linear-gradient(to bottom, #ffffff 40%, #ff8f4a 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.dpLeftSgMkConQkWz2 {
  background: linear-gradient(to bottom, #ffffff 40%, #4affe0 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.dpLeftSgMkConQkSzk1 {
  left: calc(100vw * 0.036);
}
.dpLeftSgMkConQkSzk2 {
  left: calc(100vw * 0.052);
}
.dpLeftSgMkConQkWzFl {
  width: calc(100vw * 0.03);
  height: calc(100vh * 0.025);
  position: absolute;
  top: calc(100vh * 0.012);
  left: calc(100vw * 0.07);
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 18 / 1920);
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #ffffff 40%, #4accff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.dpLeftSgMkConQkWzFl1 {
  width: calc(100vw * 0.03);
  height: calc(100vh * 0.025);
  position: absolute;
  top: calc(100vh * 0.012);
  left: calc(100vw * 0.07);
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 18 / 1920);
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #ffffff 40%, #ff8f4a 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.dpLeftSgMkConQkWzFl2 {
  width: calc(100vw * 0.03);
  height: calc(100vh * 0.025);
  position: absolute;
  top: calc(100vh * 0.012);
  left: calc(100vw * 0.07);
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 18 / 1920);
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #ffffff 40%, #4affe0 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.dpRight {
  width: calc(100vw * 0.26);
  height: calc(100vh * 0.911);
  position: absolute;
  top: calc(100vh * 0.083);
  right: calc(100vw * 0.01);
}
.dpMap {
  width: calc(100vw * 0.322);
  height: calc(100vh * 0.479);
  position: absolute;
  top: calc(100vh * 0.153);
  left: calc(100vw * 0.292);
}

.dpInput {
  width: calc(100vw * 0.224);
  height: calc(100vh * 0.047);
  background: url(./img/sousuo.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.64);
  left: calc(100vw * 0.292);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.dpInputM {
  width: calc(100vw * 0.14);
  height: calc(100vh * 0.032);
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #b0d2ff;
  letter-spacing: 0;
  font-weight: 400;
}

/* ::v-deep(.el-input__inner) { */
/deep/ .el-input__inner {
  /* .dpInputM .el-input__inner { */
  background-color: transparent !important;
  border: 0px !important;
  height: calc(100vh * 0.036) !important;
  line-height: calc(100vh * 0.036) !important;
  padding: 0 0 !important;
  margin-left: calc(100vw * 0.006);
  color: #fff !important;
  font-family: SourceHanSansSC-Regular;
}

.dpSsAn {
  width: calc(100vw * 0.07);
  height: calc(100vh * 0.042);
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 16 / 1920);
  color: #eeeeff;
  letter-spacing: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.dpBottom {
  width: calc(100vw * 0.427);
  height: calc(100vh * 0.285);
  position: absolute;
  background: url(./img/box02.png) no-repeat center;
  background-size: 100% 100%;
  top: calc(100vh * 0.694);
  left: calc(100vw * 0.292);
}

.dpTableBtn {
  width: calc(100vw * 0.41);
  height: calc(100vh * 0.259);
  position: absolute;
  top: calc(100vh * 0.015);
  left: calc(100vw * 0.008);
  overflow: hidden;
}

.dpTableTitle {
  width: 100%;
  height: calc(100vh * 0.036);
  background-image: linear-gradient(
    180deg,
    #007fbc 0%,
    rgba(0, 55, 198, 0) 100%
  );
  margin-bottom: calc(100vh * 0.001);
}

.dpTableTitle ul li {
  float: left;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 14 / 1920);
  color: #ddeeff;
  font-weight: 500;
  line-height: calc(100vh * 0.035);
  text-align: center;
  list-style-type: none;
}

.dpTableRightTitle {
  width: 100%;
  height: calc(100vh * 0.037);
  background-image: linear-gradient(
    180deg,
    #007fbc 0%,
    rgba(0, 55, 198, 0) 100%
  );
  margin-bottom: calc(100vh * 0.001);
}

.dpTableRightTitle ul li {
  float: left;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 14 / 1920);
  color: #ddeeff;
  font-weight: 500;
  line-height: calc(100vh * 0.035);
  text-align: center;
  list-style-type: none;
}

.dpTableRightTitle ul li:nth-child(1) {
  width: calc(100vw * 0.02);
}

.dpTableRightTitle ul li:nth-child(2) {
  width: calc((100% - (100vw * 0.02)) / 2);
}

.dpTableRightTitle ul li:nth-child(3) {
  width: calc((100% - (100vw * 0.02)) / 2);
}

.dpTableTitle ul li:nth-child(1) {
  width: calc(100vw * 0.033);
}

.dpTableTitle ul li:nth-child(2) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(3) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(4) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(5) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(6) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(7) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(8) {
  width: calc((100% - (100vw * 0.048)) / 8);
}

.dpTableTitle ul li:nth-child(9) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(10) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableCon {
  width: 100%;
  height: calc(100vh * 0.222);
  overflow-y: scroll;
}

.dpTableConZwsj {
  width: 100%;
  height: calc(100vh * 0.222);
  background: rgba(0, 51, 119, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: calc(100vw * 14 / 1920);
  font-family: SourceHanSansSC-Bold;
}

.dpTableConMh {
  width: 100%;
  height: calc(100vh * 0.036);
  background: rgba(0, 51, 119, 0.5);
  margin-bottom: calc(100vh * 0.001);
}

.dpTableRightCon {
  width: 100%;
  height: calc(100vh * 0.19);
  overflow-y: scroll;
}

.dpTableRightConZwsj {
  width: 100%;
  height: calc(100vh * 0.37);
  background: rgba(0, 51, 119, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: calc(100vw * 14 / 1920);
  font-family: SourceHanSansSC-Bold;
}

.dpTableRightConMh {
  width: 100%;
  height: calc(100vh * 0.037);
  background: rgba(0, 51, 119, 0.5);
  margin-bottom: calc(100vh * 0.001);
}

.table-text1 {
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 14 / 1920);
  display: inline-block;
  float: left;
  line-height: calc(100vh * 0.037);
  text-align: center;
  width: calc(100vw * 0.02);
  color: #fff;
}

.table-text {
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 14 / 1920);
  display: inline-block;
  float: left;
  line-height: calc(100vh * 0.037);
  text-align: center;
  width: calc(100vw * 0.033);
  color: #fff;
}

.tb-item {
  float: left;
  width: calc((100% - (100vw * 0.048)) / 8);
  line-height: calc(100vh * 0.037);
  font-size: calc(100vw * 14 / 1920);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
  font-family: SourceHanSansSC-Regular;
}

.tb-cu {
  cursor: pointer;
}

.tb-item2 {
  float: left;
  width: calc((100% - (100vw * 0.02)) / 2);
  line-height: calc(100vh * 0.037);
  font-size: calc(100vw * 14 / 1920);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
  font-family: SourceHanSansSC-Regular;
}

.text-color1 {
  color: #fff05f;
}

.text-color2 {
  color: #6cffd6;
}

.text-color3 {
  color: #00cbe9;
}

.dpTableRight {
  width: calc(100vw * 0.089);
  height: calc(100vh * 0.407);
  position: absolute;
  top: calc(100vh * 0.41);
  left: calc(100vw * 0.63);
}

.dpBingTu {
  width: calc(100vw * 0.18);
  height: calc(100vh * 0.185);
  position: absolute;
  top: calc(100vh * 0.044);
  right: calc(100vw * 0.01);
}

.dpPfpm {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.655);
  background: url(./img/pingfenpaimingwaikuang.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.248);
  right: calc(100vw * 0.01);
  padding: calc(100vh * 0.06) calc(100vw * 0.009) calc(100vh * 0.019)
    calc(100vw * 0.01);
}

.dpPfpmCon {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.655);
  overflow-y: scroll;
}

.dpDwphb {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.028);
  margin-bottom: calc(100vh * 0.013);
}

.dpJdtTitle {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.019);
  background-image: linear-gradient(
    270deg,
    rgba(238, 238, 238, 0) 0%,
    rgba(0, 109, 252, 0.5) 100%
  );
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.dpJdtTitleLeft {
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  text-shadow: 0 2px 2px rgba(11, 29, 62, 0.5);
  font-weight: 400;
  margin-left: calc(100vw * 0.002);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dpJdtTitleRight {
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 18 / 1920);
  color: #6bf0f9;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
}

.dpJdtTitleRight1 {
  color: #89fa56;
}

.dpJdtTitleRight2 {
  color: #25d06b;
}

.dpJdtTitleRight3 {
  color: #f9bf20;
}

.dpJdtTitleRight4 {
  color: #f66505;
}

.dpJdtTitleRight5 {
  color: #c62732;
}

/deep/.el-progress {
  line-height: 0;
}

.custom-progress >>> .el-progress-bar__outer {
  height: calc(100vh * 0.009) !important;
  /* 修改为你想要的高度 */
  background: rgba(4, 43, 103, 0.51) !important;
  border-radius: 0;
}

.custom-progress >>> .el-progress-bar__inner {
  border-radius: 0px 10px 10px 0;
  background-image: linear-gradient(90deg, #39b1ff 0%, #68ffe9 100%);
  line-height: 0;
}

.custom-progress1 >>> .el-progress-bar__inner {
  background-image: linear-gradient(270deg, #8cff54 0%, #0f41aa 100%);
}

.custom-progress2 >>> .el-progress-bar__inner {
  background-image: linear-gradient(270deg, #25d469 0%, #0f41aa 100%);
}

.custom-progress3 >>> .el-progress-bar__inner {
  background-image: linear-gradient(270deg, #fec21e 0%, #0f41aa 100%);
}

.custom-progress4 >>> .el-progress-bar__inner {
  background-image: linear-gradient(270deg, #ff6700 0%, #0f41aa 100%);
}

.custom-progress5 >>> .el-progress-bar__inner {
  background-image: linear-gradient(270deg, #d42529 0%, #0f41aa 100%);
}

.custom-progress >>> .el-progress-bar__innerText {
  display: none;
}
</style>