<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span
        class="title"
        :class="[sbxxQhVal == 1 ? 'title1' : '']"
        @click="sbxxClick(1)"
        >U位异常记录</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 2 ? 'title1' : '']"
        @click="sbxxClick(2)"
        >门磁信息记录</span
      >
    </div>

    <div style="height: 100%" v-if="sbxxQhVal == 1">
      <el-table
        :data="UwycList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="资产编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="资产名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="alertStartTime"
          label="异常操作时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="createName"
          label="操作人员"
          sortable
        ></el-table-column>
        <el-table-column
          prop="reason"
          label="异常原因"
          sortable
        ></el-table-column>
        <el-table-column prop="" label="备注" sortable></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 2">
      <el-table
        :data="McycList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="computerRoomId"
          label="场所编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetId"
          label="机柜编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetName"
          label="机柜名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="gmztName"
          label="柜门状态"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTypeName"
          label="开锁原因"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockName"
          label="开锁人员"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTime"
          label="开锁时间"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { mcQueryPage, uwQueryPage } from "../../../../api/index";

export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        id: "",
        computerRoomCode: "",
        computerRoomName: "",
        cabinetNumber: "",
        serverNumber: "",
        switchNumber: "",
        routerNumber: "",
        address: "",
        lastInspectionTime: "",
        lastInspectionResult: "",
        remark: "",
        useStatus: 1,
        approveStatus: "",
      },
      tjType: "",
      id: "",
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      spztList: [
        { id: 1, mc: "正在审批" },
        { id: 2, mc: "审批通过" },
        { id: 3, mc: "审批拒绝" },
      ],
      sbxxQhVal: 1,

      xjList: [],
      ksList: [],
      UwycList: [],
      McycList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      disabled: false,
      show: true, //是否显示
      isPrinting: false, //是否正在打印
      dialogVisible: false, //弹框
      img: "",
      xjgddyQbj: {},
    };
  },
  computed: {},
  mounted() {
    this.tjType = this.$route.query.routeType;
    this.uwQueryPage();
  },
  methods: {
    sbxxClick(index) {
      console.log(index);
      this.sbxxQhVal = index;
      if (this.sbxxQhVal === 1) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 2) {
        this.mcQueryPage();
      }
    },
    async mcQueryPage() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        equipmentCode: this.$route.query.equipmentCode,
        relocationId: this.$route.query.relocationId,
        unlockType: 2,
      };
      let data = await mcQueryPage(params);
      if (data.code === 10000) {
        this.McycList = data.data.records;
        this.total = data.data.total;
      } else {
        this.$message.error("查询详情信息失败：" + data.message);
      }
    },

    async uwQueryPage() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        equipmentCode: this.$route.query.equipmentCode,
        relocationId: this.$route.query.relocationId,
        unlockType: 2,
      };
      let data = await uwQueryPage(params);
      if (data.code === 10000) {
        this.UwycList = data.data.records;
        this.total = data.data.total;
      } else {
        this.$message.error("查询详情信息失败：" + data.message);
      }
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      if (this.sbxxQhVal === 1) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 2) {
        this.mcQueryPage();
      }
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      if (this.sbxxQhVal === 1) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 2) {
        this.mcQueryPage();
      }
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}
.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}
/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}
.flex {
  width: 950px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-select .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}
/deep/.el-textarea .el-textarea__inner {
  width: 830px !important;
  height: 129px !important;
  border-radius: 2px;
}
.ml-10 {
  background: #20bdd1;
  border: 1px solid #20bdd1;
  color: #ffffff;
}
/deep/.el-dialog__wrapper .el-dialog {
  background-image: linear-gradient(180deg, #f0f7fe 0%, #ffffff 32%);
  border: 1px solid rgba(151, 151, 151, 1);
  border-radius: 4px;
  padding-left: 6px;
  padding-right: 6px;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__header {
  padding: 15px 14.5px 13.5px 14px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
}
/deep/.el-dialog__wrapper .el-dialog .el-dialog__body {
  padding: 0;
}
.formDialog {
  padding: 23.5px 45px 25px 45px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #000116;
  font-weight: 400;
  position: relative;
}
.formDialog1 {
  padding: 38px 45px 49px 45px;
  margin-bottom: 10px;
}
.flexAlign {
  display: flex;
  align-items: center;
}
.formDialogItem {
  width: 100px;
  height: 24px;
  margin-right: 10px;
}
.formDialogCon {
  width: 320px;
  height: 24px;
}
.formDialogCon1 {
  width: 168px;
  height: 24px;
}
.fjx {
  width: 510px;
  height: 1px;
  background-color: rgba(229, 229, 229, 1);
  margin: 0 auto;
}
.ewm {
  position: absolute;
  width: 145px;
  height: 145px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 31.54px;
  border: 4px solid #d8e4fa;
  border-radius: 10px;
}
.btnRight {
  height: 40px;
  position: relative;
}
.btnRightItem {
  position: absolute;
  right: 31.54px;
}
.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}
</style>
