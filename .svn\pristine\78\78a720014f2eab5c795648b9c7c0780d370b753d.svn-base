<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div class="bg_con_top">
      <img src="./img/title.png" alt="" />
      <span
        class="title"
        :class="[sbxxQhVal == 1 ? 'title1' : '']"
        @click="sbxxClick(1)"
        >场所巡检信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 2 ? 'title1' : '']"
        @click="sbxxClick(2)"
        >机柜巡检信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 3 ? 'title1' : '']"
        @click="sbxxClick(3)"
        >设备巡检信息</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 4 ? 'title1' : '']"
        @click="sbxxClick(4)"
        >U位异常记录</span
      >
      <span
        class="title"
        :class="[sbxxQhVal == 5 ? 'title1' : '']"
        @click="sbxxClick(5)"
        >门磁信息记录</span
      >
    </div>
    <div v-if="sbxxQhVal == 1">
      <el-form ref="form" :model="form" size="mini" label-width="100px">
        <div class="flex">
          <el-form-item label="场所编号">
            <el-input v-model="form.computerRoomCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="场所名称">
            <el-input v-model="form.computerRoomName" disabled></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="清洁度">
            <el-input v-model="form.cleanliness" disabled></el-input>
          </el-form-item>
          <el-form-item label="空调">
            <el-input v-model="form.airConditioning" disabled></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="场所动力环境系统运行">
            <el-input v-model="form.powerEnvironmentSystem" disabled></el-input>
          </el-form-item>
          <el-form-item label="巡检时间">
            <el-input v-model="form.inspectionTime" disabled></el-input>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="巡检人员编号">
            <el-input v-model="form.inspectionCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="巡检人员名称">
            <el-input v-model="form.inspectionName" disabled></el-input>
          </el-form-item>
        </div>
        <!-- <div class="flex">
          <el-form-item label="清洁度">
            <el-input type="textarea" v-model="form.cleanliness"></el-input>
          </el-form-item>
          <el-form-item label="空调">
            <el-input type="textarea" v-model="form.airConditioning"></el-input>
          </el-form-item>
        </div> -->
        <div style="width: 925px">
          <el-form-item style="float: right">
            <el-button type="primary" @click="gojfxj">返回</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 2">
      <el-table
        :data="jfxjList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="cabinetCode"
          label="机柜编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetName"
          label="机柜名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="temperature"
          label="机柜温度"
          sortable
        ></el-table-column>
        <el-table-column
          prop="dust"
          label="机柜湿度"
          sortable
        ></el-table-column>
        <el-table-column
          prop="close"
          label="柜门关闭"
          sortable
        ></el-table-column>
        <el-table-column
          prop="humidity"
          label="机柜电力"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cleanliness"
          label="柜内灰尘"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionTime"
          label="巡检时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionCode"
          label="巡检人员编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="inspectionName"
          label="巡检人员名称"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div class="scroll-container" style="height: 100%" v-if="sbxxQhVal == 3">
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        style="height: 100%"
      >
        <el-tab-pane label="设备" name="1" style="height: 100%">
          <el-table
            :data="sbxjList"
            border
            :header-cell-style="{
              background: '#EEF7FF',
              color: '#4D91F8',
            }"
            style="width: 100%; border: 1px solid #ebeef5"
            height="calc(100% - 192px)"
            stripe
            class="table"
          >
            <el-table-column
              type="index"
              label="序号"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="equipmentCode"
              label="设备编号"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentName"
              label="设备名称"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentType"
              label="设备类型"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentOnline"
              label="设备在线"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentPower"
              label="设备电力"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentRunningSound"
              label="设备运行声音"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentWarm"
              label="设备告警"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentLoosen"
              label="设备松动"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentDowntime"
              label="设备宕机"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentHardDisk"
              label="设备硬盘"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentOhticalModule"
              label="设备光模块"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentPowerModule"
              label="设备电源模块"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentFan"
              label="设备风扇"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentTemperature"
              label="设备温度"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentPort"
              label="设备端口"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentOpticalFiber"
              label="设备光纤"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentNetworkCable"
              label="设备网线"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentLabel"
              label="设备标签"
              sortable
            ></el-table-column>
            <el-table-column
              prop="description"
              label="故障情况描述"
              sortable
            ></el-table-column>
            <el-table-column
              prop="treatment"
              label="处理办法"
              sortable
            ></el-table-column>
            <el-table-column
              prop="recoveryTime"
              label="恢复时间"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionTime"
              label="巡检时间"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionCode"
              label="巡检人员编号"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionName"
              label="巡检人员名称"
              sortable
            ></el-table-column>
          </el-table>

          <!-- 分页区域 -->
          <div style="border: 1px solid #ebeef5; margin-top: 10px">
            <el-pagination
              background
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :pager-count="5"
              :current-page="page"
              :page-sizes="[5, 10, 20, 30]"
              :page-size="pageSize"
              layout="total, prev, pager, sizes,next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="线缆" name="2" style="height: 100%">
          <el-table
            :data="sbxjList"
            border
            :header-cell-style="{
              background: '#EEF7FF',
              color: '#4D91F8',
            }"
            style="width: 100%; border: 1px solid #ebeef5"
            height="calc(100% - 192px)"
            stripe
          >
            <el-table-column
              type="index"
              width="60"
              label="序号"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="equipmentCode"
              label="设备编号"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentName"
              label="设备名称"
              sortable
            ></el-table-column>
            <el-table-column
              prop="equipmentType"
              label="设备类型"
              sortable
            ></el-table-column>
            <el-table-column
              prop="cableDamaged"
              label="线缆破损"
              sortable
            ></el-table-column>
            <el-table-column
              prop="cableRibbon"
              label="线缆扎带"
              sortable
            ></el-table-column>
            <el-table-column
              prop="cableJoint"
              label="线缆接头"
              sortable
            ></el-table-column>
            <el-table-column
              prop="cableLabel"
              label="线缆标签"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionTime"
              label="巡检时间"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionCode"
              label="巡检人员编号"
              sortable
            ></el-table-column>
            <el-table-column
              prop="inspectionName"
              label="巡检人员名称"
              sortable
            ></el-table-column>
          </el-table>
          <!-- 分页区域 -->
          <div style="border: 1px solid #ebeef5; margin-top: 10px">
            <el-pagination
              background
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :pager-count="5"
              :current-page="page"
              :page-sizes="[5, 10, 20, 30]"
              :page-size="pageSize"
              layout="total, prev, pager, sizes,next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 4">
      <el-table
        :data="UwycList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="equipmentCode"
          label="资产编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="equipmentName"
          label="资产名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="alertStartTime"
          label="异常操作时间"
          sortable
        ></el-table-column>
        <el-table-column
          prop="createName"
          label="操作人员"
          sortable
        ></el-table-column>
        <el-table-column
          prop="reason"
          label="异常原因"
          sortable
        ></el-table-column>
        <el-table-column prop="" label="备注" sortable></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <div style="height: 100%" v-if="sbxxQhVal == 5">
      <el-table
        :data="McycList"
        border
        :header-cell-style="{
          background: '#EEF7FF',
          color: '#4D91F8',
        }"
        style="width: 100%; border: 1px solid #ebeef5"
        height="calc(100% - 127px)"
        stripe
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="computerRoomId"
          label="场所编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetId"
          label="机柜编号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="cabinetName"
          label="机柜名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="gmztName"
          label="柜门状态"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTypeName"
          label="开锁原因"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockName"
          label="开锁人员"
          sortable
        ></el-table-column>
        <el-table-column
          prop="unlockTime"
          label="开锁时间"
          sortable
        ></el-table-column>
      </el-table>
      <!-- -------------------------分页区域---------------------------- -->
      <div style="border: 1px solid #ebeef5">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, prev, pager, sizes,next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getInspectionCabinetPage, //机柜巡检信息
  getInspectionEquipmentPage, //设备巡检信息
  mcQueryPage,
  uwQueryPage,
} from "../../../../api/index";
export default {
  components: {},
  props: {},
  data() {
    return {
      form: {
        name: "",
        desc: "",
      },
      sbxxQhVal: 1,
      jfxjList: [],
      sbxjList: [],
      gzclList: [],
      UwycList: [],
      McycList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      syztList: [
        { id: 1, mc: "正常" },
        { id: 2, mc: "不正常" },
      ],
      useStatus: "",
      activeName: "1",
      mainType: "",
    };
  },
  computed: {},
  mounted() {
    // this.sbxxClick();
    this.fetchDetailData();
  },
  methods: {
    fetchDetailData() {
      const data = this.$route.query.data;
      if (data) {
        this.form = JSON.parse(data);
        console.log(this.form, "=======");
        if (this.form.temperature == 1) {
          this.form.temperature = "正常";
        } else if (this.form.temperature == 2) {
          this.form.temperature = "不正常";
        }
        if (this.form.cleanliness == 1) {
          this.form.cleanliness = "正常";
        } else if (this.form.cleanliness == 2) {
          this.form.cleanliness = "不正常";
        }
        if (this.form.powerEnvironmentSystem == 1) {
          this.form.powerEnvironmentSystem = "正常";
        } else if (this.form.powerEnvironmentSystem == 2) {
          this.form.powerEnvironmentSystem = "不正常";
        }
        if (this.form.airConditioning == 1) {
          this.form.airConditioning = "正常";
        } else if (this.form.airConditioning == 2) {
          this.form.airConditioning = "不正常";
        }
      }
    },

    gojfxj() {
      this.$router.push({ path: "/jfxjsjtj" });
    },

    async getInspectionCabinetPage() {
      try {
        let params = {
          inspectionId: this.form.inspectionId,
          pageNo: this.page,
          pageSize: this.pageSize,
        };
        let data = await getInspectionCabinetPage(params);
        if (data.code === 10000) {
          this.jfxjList = data.data.records;
          this.total = data.data.total;
          // this.selectedItem = this.jfList.find(item => item.id === this.form.computerRoomId);
        } else {
          this.$message.error("查询详情信息失败：" + data.message);
        }
      } catch (error) {
        console.error("查询详情信息失败：:", error);
        this.$message.error("查询详情信息失败：" + error.message);
      }
    },

    async getInspectionEquipmentPage() {
      try {
        let params = {
          inspectionId: this.form.inspectionId,
          mainType: this.mainType,
          pageNo: this.page,
          pageSize: this.pageSize,
        };
        let data = await getInspectionEquipmentPage(params);
        if (data.code === 10000) {
          this.sbxjList = data.data.records;
          this.total = data.data.total;
          // this.selectedItem = this.jfList.find(item => item.id === this.form.computerRoomId);
        } else {
          this.$message.error("查询详情信息失败：" + data.message);
        }
      } catch (error) {
        console.error("查询详情信息失败：:", error);
        this.$message.error("查询详情信息失败：" + error.message);
      }
    },

    sbxxClick(index) {
      this.sbxxQhVal = index;
      if (this.sbxxQhVal === 2) {
        this.getInspectionCabinetPage();
      }
      if (this.sbxxQhVal === 3) {
        this.mainType = "1";
        this.getInspectionEquipmentPage();
      }
      if (this.sbxxQhVal === 4) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 5) {
        this.mcQueryPage();
      }
    },

    async mcQueryPage() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomCode: this.form.computerRoomCode,
        inspectionId: this.form.inspectionId,
        unlockType: 1,
      };
      let data = await mcQueryPage(params);
      if (data.code === 10000) {
        this.McycList = data.data.records;
        this.total = data.data.total;
      } else {
        this.$message.error("查询详情信息失败：" + data.message);
      }
    },

    async uwQueryPage() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomCode: this.form.computerRoomCode,
        inspectionId: this.form.inspectionId,
        unlockType: 1,
      };
      let data = await uwQueryPage(params);
      if (data.code === 10000) {
        this.UwycList = data.data.records;
        this.total = data.data.total;
      } else {
        this.$message.error("查询详情信息失败：" + data.message);
      }
    },

    handleClick(tab, event) {
      if (tab.name === "1") {
        this.mainType = "1";
      } else if (tab.name === "2") {
        this.mainType = "2";
      }
      this.activeName = tab.name;
      this.getInspectionEquipmentPage();
    },

    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      if (this.sbxxQhVal === 2) {
        this.getInspectionCabinetPage();
      }
      if (this.sbxxQhVal === 3) {
        this.mainType = "1";
        this.getInspectionEquipmentPage();
      }
      if (this.sbxxQhVal === 4) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 5) {
        this.mcQueryPage();
      }
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      if (this.sbxxQhVal === 2) {
        this.getInspectionCabinetPage();
      }
      if (this.sbxxQhVal === 3) {
        this.mainType = "1";
        this.getInspectionEquipmentPage();
      }
      if (this.sbxxQhVal === 4) {
        this.uwQueryPage();
      }
      if (this.sbxxQhVal === 5) {
        this.mcQueryPage();
      }
    },

    forlx(row) {
      let hxsj;
      this.syztList.forEach((item) => {
        if (row.equipmentOnline == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentPower == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentWarm == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentLoosen == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentDowntime == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentHardDisk == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentOhticalModule == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentPowerModule == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentFan == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentPort == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentOpticalFiber == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentNetworkCable == item.id) {
          hxsj = item.mc;
        }
        if (row.equipmentLabel == item.id) {
          hxsj = item.mc;
        }
        if (row.cableDamaged == item.id) {
          hxsj = item.mc;
        }
        if (row.cableRibbon == item.id) {
          hxsj = item.mc;
        }
        if (row.cableJoint == item.id) {
          hxsj = item.mc;
        }
        if (row.cableLabel == item.id) {
          hxsj = item.mc;
        }
        if (row.cableColor == item.id) {
          hxsj = item.mc;
        }
        if (row.temperature == item.id) {
          hxsj = item.mc;
        }
        if (row.humidity == item.id) {
          hxsj = item.mc;
        }
        if (row.cleanliness == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  padding: 10px;
}

.bg_con_top {
  width: 100%;
  height: 35px;
  font-family: SourceHanSansSC-Medium;
  font-size: 18px;
  color: #080808;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(229, 229, 229, 1);
  margin-bottom: 20px;
}

/deep/.el-form-item .el-form-item__label {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
  text-align: left;
}

.flex {
  width: 925px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/deep/.el-input .el-input__inner {
  width: 350px !important;
  border-radius: 2px !important;
}

/deep/.el-textarea .el-textarea__inner {
  width: 829px !important;
  height: 129px !important;
  border-radius: 2px;
}

.title {
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  position: relative;
}

.title1::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -15px;
  background-color: #0077d2;
}

/deep/ .el-tabs__content {
  height: 100%;
}
.table ::-webkit-scrollbar:horizontal {
  display: block !important;
}
</style>
