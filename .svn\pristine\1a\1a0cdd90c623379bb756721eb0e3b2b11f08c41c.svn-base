<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div
      style="width: 100%; position: relative; overflow: hidden; height: 100%"
    >
      <div class="dabg" style="height: 100%">
        <div class="content" style="height: 100%">
          <div class="table" style="height: 100%">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form
                :inline="true"
                :model="formInline"
                size="medium"
                class="demo-form-inline"
                style="float: left"
              >
                <el-form-item style="font-weight: 700" label="场所名称">
                  <el-input
                    v-model="formInline.computerRoomName"
                    clearable
                    placeholder="场所名称"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700" label="场所编号">
                  <el-input
                    v-model="formInline.computerRoomCode"
                    clearable
                    placeholder="场所编号"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700" label="机柜名称">
                  <el-input
                    v-model="formInline.cabinetName"
                    clearable
                    placeholder="机柜名称"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700" label="机柜编号">
                  <el-input
                    v-model="formInline.cabinetCode"
                    clearable
                    placeholder="机柜编号"
                    class="widths"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="onSubmit"
                    >查询</el-button
                  >
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="warning"
                    icon="el-icon-circle-close"
                    @click="cz"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
              <el-form
                :inline="true"
                :model="formInline"
                size="medium"
                class="demo-form-inline"
                style="float: right"
              >
                <!-- <el-form-item style="float: right">
                  <el-button
                    v-if="this.dwjy"
                    type="danger"
                    size="medium"
                    @click="shanchu"
                    icon="el-icon-delete-solid"
                  >
                    删除
                  </el-button>
                </el-form-item> -->
                <el-form-item style="float: right">
                  <el-button
                    type="danger"
                    size="medium"
                    @click="shanchu"
                    icon="el-icon-delete-solid"
                  >
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right">
                  <el-button
                    type="success"
                    size="medium"
                    @click="xzaqcp"
                    icon="el-icon-plus"
                  >
                    新增
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%">
              <div class="table_content" style="height: 100%">
                <el-table
                  :data="aqcpList"
                  border
                  @selection-change="selectRow"
                  :header-cell-style="{
                    background: '#EEF7FF',
                    color: '#4D91F8',
                  }"
                  style="width: 100%; border: 1px solid #ebeef5"
                  height="calc(100% - 34px - 41px - 3px)"
                  stripe
                >
                  <el-table-column type="selection" width="55" align="center">
                  </el-table-column>
                  <el-table-column
                    type="index"
                    width="60"
                    label="序号"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomCode"
                    label="场所编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomName"
                    label="场所名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="cabinetCode"
                    label="机柜编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="cabinetName"
                    label="机柜名称"
                    sortable
                  ></el-table-column>
                  <!--                  <el-table-column prop="serverNumber" label="服务器数量"></el-table-column>
                  <el-table-column prop="switchNumber" label="交换机数量"></el-table-column>
                  <el-table-column prop="routerNumber" label="路由器数量"></el-table-column>-->
                  <!--                  <el-table-column prop="lastInspectionTime" label="上次巡检时间" sortable></el-table-column>
                  <el-table-column prop="lastInspectionResult" label="上次巡检结果" sortable></el-table-column>-->
                  <el-table-column
                    prop="useStatus"
                    label="使用状态"
                    :formatter="forlx"
                    sortable
                  ></el-table-column>
                  <!-- <el-table-column
                    prop="approveStatus"
                    label="审批状态"
                    :formatter="forsplx"
                  ></el-table-column> -->
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button
                        size="medium"
                        type="text"
                        @click="updateItem(scoped.row)"
                        >修改
                      </el-button>
                      <el-button
                        size="medium"
                        type="text"
                        @click="xqyl(scoped.row)"
                        >详情
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="" label="历史数据" width="120">
                    <template slot-scope="scoped">
                      <el-button
                        size="medium"
                        type="text"
                        @click="wdClick(scoped.row)"
                        >温度湿度
                      </el-button>
                      <!-- <el-button
                        size="medium"
                        type="text"
                        @click="sdClick(scoped.row)"
                        >湿度
                      </el-button> -->
                    </template>
                  </el-table-column>
                </el-table>
                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5">
                  <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :pager-count="5"
                    :current-page="page"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper"
                    :total="total"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="温度数据"
      :visible.sync="wddialogVisible"
      width="1100px"
      :before-close="handleClose"
    >
      <div class="top_title">
        <el-date-picker
          v-model="tMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月份"
        >
        </el-date-picker>
        <el-button type="primary" @click="tMonthClick">查询</el-button>
      </div>

      <div ref="charts" id="main" style="width: 1040px; height: 600px"></div>
      <div style="color: #fff;font-size: 20px;font-weight: 700;">湿度数据</div>
      <!-- <div class="top_title">
        <el-date-picker
          v-model="hMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月份"
        >
        </el-date-picker>
        <el-button type="primary" @click="hMonthClick">查询</el-button>
      </div> -->
      <div ref="charts" id="main1" style="width: 1040px; height: 600px"></div>
    </el-dialog>
    <!-- <el-dialog
      title="湿度数据"
      :visible.sync="sddialogVisible"
      width="60%"
      :before-close="handleClose"
    >
      <div class="top_title">
        <el-date-picker
          v-model="hMonth"
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择月份"
        >
        </el-date-picker>
        <el-button type="primary" @click="hMonthClick">查询</el-button>
      </div>
      <div ref="charts" id="main1" style="width: 1112px; height: 600px"></div>
    </el-dialog> -->
  </div>
</template>
<script>
import * as echarts from "echarts";

import {
  queryCabinetPage, //分页查询
  jgQueryByCondition,
  insertCabinet,
  // updateCabinet,
  deleteCabinet,
  getCabinetTemerature,
  getCabinetHumidity,
} from "../../../../api/shma";

export default {
  components: {},
  props: {},
  data() {
    return {
      aqcpList: [], //列表数据

      formInline: {}, //查询区域数据

      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
      wddialogVisible: false, //确认框
      sddialogVisible: false, //提示框
      tMonth: "", //选择月份
      hMonth: "", //选择月份
      categoriesTlist: [],
      categoriesHlist: [],
      seriesTlist: [],
      seriesHlist: [],
      cabinetId: "", //机柜id
    };
  },
  computed: {},
  mounted() {
    this.aqcp();
    // this.initCharts();
  },

  methods: {
    tMonthClick() {
      // console.log("tMonthClick triggered");
      this.wdGet();
      this.sdGet();
    },
    // hMonthClick() {
    //   this.sdGet();
    // },
    wdClick(row) {
      this.cabinetId = row.id;
      // 获取当前年月格式为yyyy-MM
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      if (month < 10) {
        month = "0" + month;
      }
      this.tMonth = year + "-" + month;
      this.wdGet();
      this.sdGet();
    },
    async wdGet() {
      console.log("wdClick triggered");
      let params = {
        cabinetId: this.cabinetId,
        date: this.tMonth,
      };
      let data = await getCabinetTemerature(params);
      if (data.code == 10000) {
        this.categoriesTlist = data.data.categories;
        this.seriesTlist = data.data.series;
        this.wddialogVisible = true;
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    initCharts() {
      var chartDom = document.getElementById("main");
      var myChart = echarts.init(chartDom);
      var option;
      let seriesData = {
        categories: this.categoriesTlist,
        series: this.seriesTlist,
      };
      const colorList = ["#1678FF", "#00D052", "#F6ED0D", "#FD2BFF", "#9E87FF"];
      let series = [];
      seriesData.series.forEach((item, index) => {
        console.log(seriesData.series.length - 1, index, "0909");
        series.push({
          name: item.name,
          type: "line",
          data: item.data,
          symbolSize: 7,
          symbol: "circle",
          smooth: true,
          // yAxisIndex: seriesData.series.length - 1 == index ? 1 : 0,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: colorList[index],
              },
              {
                offset: 1,
                color: colorList[index],
              },
            ]),
          },
          itemStyle: {
            color: colorList[index], // 设置圆形的填充色
            borderColor: "#ffffff", // 设置边框颜色
            borderWidth: 1, // 设置边框宽度
          },
          markLine: {
            data: [
              {
                name: "规定最高温度28℃",
                yAxis: 28,
                lineStyle: {
                  color: "red",
                  type: "solid",
                },
              },
              {
                name: "规定最低温度10℃",
                yAxis: 10,
                lineStyle: {
                  color: "blue",
                  type: "solid",
                },
              },
            ],
            symbol: "none", // 不显示符号
            label: {
              show: true,
              position: "end",
              formatter: "{b}",
            },
          },
        });
      });
      option = {
        backgroundColor: "#2e5495",
        //你的代码
        legend: {
          icon: "circle",
          top: "5%",
          right: "5%",
          itemWidth: 6,
          itemGap: 20,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "25%",
          bottom: "15%",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var str = `${params[0].name}<br/>`;
            params.forEach((par) => {
              str += `${par.marker} ${par.seriesName}：${
                par.value
              } ${(par.seriesName = "℃")}<br/>`;
            });
            return str;
          },
        },
        xAxis: [
          {
            type: "category",
            data: seriesData.categories ? seriesData.categories : [],
            axisLine: {
              lineStyle: {
                color: "#3DECFF",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#fff",
              },
              // 默认x轴字体大小
              fontSize: 12,
              // margin:文字到x轴的距离
              margin: 15,
            },
            axisPointer: {
              label: {
                // padding: [11, 5, 7],
                padding: [0, 0, 10, 0],
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
                backgroundColor: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      // offset: 0.9,
                      offset: 0.86,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      offset: 0.86,
                      color: "#33c0cd", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#33c0cd", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：℃",
            nameTextStyle: {
              color: "#fff",
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "rgba(255,255,255,0.5)",
              },
            },
          },
          {
            splitLine: {
              show: false,
            },
          },
        ],
        series: [...series],
      };

      option && myChart.setOption(option);
    },
    sdClick(row) {
      this.cabinetId = row.id;
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      if (month < 10) {
        month = "0" + month;
      }
      this.hMonth = year + "-" + month;
      this.wdGet();
      this.sdGet();
    },
    async sdGet() {
      let params = {
        cabinetId: this.cabinetId,
        date: this.tMonth,
      };
      let data = await getCabinetHumidity(params);
      if (data.code == 10000) {
        this.categoriesHlist = data.data.categories;
        this.seriesHlist = data.data.series;
        this.wddialogVisible = true;
        this.$nextTick(() => {
          this.initCharts1();
        });
      }
    },
    initCharts1() {
      var chartDom = document.getElementById("main1");
      var myChart = echarts.init(chartDom);
      var option;
      let seriesData = {
        categories: this.categoriesHlist,
        series: this.seriesHlist,
      };
      const colorList = ["#1678FF", "#00D052", "#F6ED0D", "#FD2BFF", "#9E87FF"];
      let series = [];
      seriesData.series.forEach((item, index) => {
        console.log(seriesData.series.length - 1, index, "0909");
        series.push({
          name: item.name,
          type: "line",
          data: item.data,
          symbolSize: 7,
          symbol: "circle",
          smooth: true,
          // yAxisIndex: seriesData.series.length - 1 == index ? 1 : 0,
          showSymbol: true,
          lineStyle: {
            width: 2,
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: colorList[index],
              },
              {
                offset: 1,
                color: colorList[index],
              },
            ]),
          },
          itemStyle: {
            color: colorList[index], // 设置圆形的填充色
            borderColor: "#ffffff", // 设置边框颜色
            borderWidth: 1, // 设置边框宽度
          },
          markLine: {
            data: [
              {
                name: "规定最高湿度70%",
                yAxis: 70,
                lineStyle: {
                  color: "red",
                  type: "solid",
                },
              },
              {
                name: "规定最低湿度30%",
                yAxis: 30,
                lineStyle: {
                  color: "blue",
                  type: "solid",
                },
              },
            ],
            symbol: "none", // 不显示符号
            label: {
              show: true,
              position: "end",
              formatter: "{b}",
            },
          },
        });
      });
      option = {
        backgroundColor: "#2e5495",
        //你的代码
        legend: {
          icon: "circle",
          top: "5%",
          right: "5%",
          itemWidth: 6,
          itemGap: 20,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "25%",
          bottom: "15%",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            var str = `${params[0].name}<br/>`;
            params.forEach((par) => {
              str += `${par.marker} ${par.seriesName}：${
                par.value
              } ${(par.seriesName = "℃")}<br/>`;
            });
            return str;
          },
        },
        xAxis: [
          {
            type: "category",
            data: seriesData.categories ? seriesData.categories : [],
            axisLine: {
              lineStyle: {
                color: "#3DECFF",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#fff",
              },
              // 默认x轴字体大小
              fontSize: 12,
              // margin:文字到x轴的距离
              margin: 15,
            },
            axisPointer: {
              label: {
                // padding: [11, 5, 7],
                padding: [0, 0, 10, 0],
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
                backgroundColor: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      // offset: 0.9,
                      offset: 0.86,
                      color: "#fff", // 0% 处的颜色
                    },
                    {
                      offset: 0.86,
                      color: "#33c0cd", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#33c0cd", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：%",
            nameTextStyle: {
              color: "#fff",
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: "dashed",
                color: "rgba(255,255,255,0.5)",
              },
            },
          },
          {
            splitLine: {
              show: false,
            },
          },
        ],
        series: [...series],
      };

      option && myChart.setOption(option);
    },
    //新增数据按钮事件
    xzaqcp() {
      this.$router.push({
        path: "/jgglXqxg",
        query: {
          routeType: "1",
        },
      });
    },

    //详情弹框
    xqyl(row) {
      this.$router.push({
        path: "/jgglXqxg",
        query: {
          id: row.id,
          routeType: "3",
        },
      });
    },
    //修改弹框
    updateItem(row) {
      this.$router.push({
        path: "/jgglXqxg",
        query: {
          id: row.id,
          routeType: "2",
        },
      });
    },
    //查询
    onSubmit() {
      this.page = 1;
      this.aqcp();
    },
    //获取列表的值
    async aqcp() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomName: this.formInline.computerRoomName,
        cabinetName: this.formInline.cabinetName,
        computerRoomCode: this.formInline.computerRoomCode,
        cabinetCode: this.formInline.cabinetCode,
      };
      let resList = await queryCabinetPage(params);
      console.log("resList", resList);
      this.aqcpList = resList.data.records;
      this.total = resList.data.total;
    },
    //删除
    shanchu(id) {
      let that = this;
      if (this.selectlistRow != "") {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            deleteCabinet(this.selectlistRow).then(() => {
              that.aqcp();
            });
            this.$message({
              message: "删除成功",
              type: "success",
            });
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: "未选择删除记录，请选择下列列表",
          type: "warning",
        });
      }
    },

    //选中列表的数据
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.aqcp();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.aqcp();
    },

    cz() {
      this.formInline = {};
    },
    forlx(row) {
      let hxsj;
      this.syztList.forEach((item) => {
        if (row.useStatus == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.cd {
  width: 191px;
}

/deep/.el-form--inline .el-form-item {
  margin-right: 9px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}
/deep/ .el-dialog {
  background: #2e5495 !important;
}
/deep/ .el-dialog__title {
  color: #fff !important;
}
.top_title {
  margin-bottom: 20px;
  float: left;
  position: relative;
  z-index: 99999;
  margin-left: 85px;
  margin-top: 25px;
}

/deep/.el-dialog__body {
  padding: 0px !important;
}
</style>
