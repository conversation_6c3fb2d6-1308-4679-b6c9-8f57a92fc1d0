<template>
  <div class="out">
    <div class="card" @click="minimizeSystem()">
      <!-- <img src="../../assets/icons/zxh.png" /> -->
      <!-- <span>最小化</span> -->
      <i class="el-icon-minus" style="color: #FEC13F;"></i>
    </div>
    <div class="card" @click="maximizeSystem()">
      <i v-show="!windowBtnConfig.isMax" class="el-icon-full-screen" style="color:#559DF7;"></i>
      <i v-show="windowBtnConfig.isMax" class="el-icon-copy-document" style="color:#559DF7;"></i>
      <!-- <span v-show="!windowBtnConfig.isMax">最大化</span> -->
      <!-- <span v-show="windowBtnConfig.isMax">窗口化</span> -->
    </div>
    <div class="card">
      <!-- <img src="../../assets/icons/header_icon8.png" /> -->
      <el-popover placement="bottom" width="80" trigger="hover">
        <div class="system-setting-out">
          <div v-for="(item,index) in systemSettingList" v-if="(item.showRoles.indexOf('all') != -1)||(item.showRoles.indexOf(currentYhm) != -1)" :key="index" @click="toPath(item.path)"><i :class="item.icon"></i>{{item.name}}</div>
        </div>
        <!-- <span slot="reference">系统设置</span> -->
        <!-- <img slot="reference" src="../../assets/icons/header_icon8.png" style="margin-top: 5px;" /> -->
        <i slot="reference" class="el-icon-setting" style="color: #909399;"></i>
      </el-popover>
    </div>
    <div class="card" @click="quitSystem()">
      <!-- <img src="../../assets/icons/header_icon9.png" /> -->
      <!-- <span>退出</span> -->
      <i class="el-icon-close" style="color: #FE645D;"></i>
    </div>
  </div>
</template>

<script>
// import { removeWindowLocation, getWindowLocation } from '../../../utils/windowLocation'

// import { writeLoginLog } from '../../../utils/logUtils'

export default {
  data () {
    return {
      // 当前登录用户的用户名(目前系统没有角色概念，故直接使用账号进行按钮显隐的判断)（用户名从登录时放入缓存的数据获取）
      currentYhm: '',
      /**
       * 最大化按钮控制配置
       * 窗口化时显示最大化
       * 最大化时显示窗口化
      */
      windowBtnConfig: {
        isMax: false
      },
      // 系统设置菜单集合
      systemSettingList: [
        {
          name: '密码重置',
          path: '/mmczSetting',
          icon: 'el-icon-edit',
          // 判断按钮是否需要隐藏（目前系统没有角色概念，故直接使用账号进行判断）
          showRoles: ['admin']
        },
        {
          name: '修改密码',
          path: '/xgmmSetting',
          icon: 'el-icon-edit',
          showRoles: ['all']
        },
        {
          name: '参数设置',
          path: '/systemSetting',
          icon: 'el-icon-notebook-2',
          showRoles: ['all']
        },
        // {
        //   name: '文件路径设置',
        //   path: '/filePathSetting',
        //   icon: 'el-icon-files',
        //   showRoles: ['all']
        // },
        {
          name: '代码管理',
          path: '/dmglSetting',
          icon: 'el-icon-c-scale-to-original',
          showRoles: ['all']
        },
        // {
        //   name: '数据上报',
        //   path: '/sjsbSetting',
        //   icon: 'el-icon-bank-card',
        //   showRoles: ['all']
        // },
        {
          name: '注册信息',
          path: '/zcxxSetting',
          icon: 'el-icon-office-building',
          showRoles: ['all']
        },
        {
          name: '关于我们',
          path: '/gywmSetting',
          icon: 'el-icon-message',
          showRoles: ['all']
        },
        {
          name: '工具箱',
          path: '/toolBox',
          icon: 'el-icon-message',
          showRoles: ['all']
        },
        {
          name: '退出登录',
          path: '/quit',
          icon: 'el-icon-warning-outline',
          showRoles: ['all']
        }
      ]
    }
  },
  methods: {
    // 关闭 el-popover 弹出窗
    closeElPopover () {
      // console.log('closeElPopover mouseleave')
      this.elPopoverFlag = false
    },
    closeElPopover () {
      // console.log('closeElPopover mouseleave')
      this.elPopoverFlag = false
    },
    // 页面跳转方法
    toPath (path) {
      if (path == '/quit') {
        // 写入登录日志
        // writeLoginLog(1)
        // 清除缓存
        // removeWindowLocation()
        this.$router.push('/')
        return
      }
      this.$router.push(path)
    },
    // 最小化系统
    minimizeSystem () {
      this.$electron.ipcRenderer.send("hide-window")
    },
    // 最大化系统
    maximizeSystem () {
      let isMax = this.windowBtnConfig.isMax
      this.windowBtnConfig.isMax = !isMax
      if (isMax) {
        this.$electron.ipcRenderer.send("unmax-window")
      } else {
        this.$electron.ipcRenderer.send("max-window")
      }
    },
    // 退出系统
    quitSystem () {
      this.$confirm('是否执行此操作，点击将退出系统？', '是否退出系统？', {
        cancelButtonClass: "btn-custom-cancel",
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // center: true
      }).then(() => {
        this.$emit('quitClicked', true)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消退出系统'
        })
      })
    }
  },
  mounted () { },
  watch: {
    // 每次路由变动都去扫一遍缓存中的用户信息（为了处理账号切换时设置里的菜单未正确显隐问题）
    $route (to, form) {
      // console.log('路由变更，校验设置里的菜单')
      // let localObj = getWindowLocation()
      // this.currentYhm = localObj.yhm
    }
  }
}
</script>

<style scoped>
.out {
  display: flex;
  height: 100%;
  /* background: red; */
  color: white;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  align-self: center;
  width: 100%;
  border-radius: 18px;
}
.out .card {
  flex: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  /* flex-direction: column; */
}
/* .out .card:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.18);
} */
.out .card:nth-child(odd) {
  /* background: green; */
}
.out .card img {
  /* margin: 5px 10px 5px 0; */
  width: 18px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.5);
  padding: 5px;
  /* box-sizing: border-box; */
  border-radius: 50%;
}
.out .card img:hover {
}
.out .card i {
  /* margin: 5px 10px 5px 0; */
  font-size: 18px;
  cursor: pointer;
  background: rgba(255, 255, 255, 1);
  padding: 5px;
  /* box-sizing: border-box; */
  border-radius: 50%;
  /* color: red; */
  font-weight: bold;
}
.out .card i:hover {
}
/**系统设置菜单**/
.system-setting-out {
  /* background: red; */
  font-size: 13px;
}
.system-setting-out div {
  border-radius: 6px;
  margin: 3px 0;
  cursor: pointer;
  padding: 3px 0 3px 15px;
}
.system-setting-out div:hover {
  background: #f4f4f5;
  color: #409eff;
}
.system-setting-out > div i {
  margin-right: 10px;
}
</style>