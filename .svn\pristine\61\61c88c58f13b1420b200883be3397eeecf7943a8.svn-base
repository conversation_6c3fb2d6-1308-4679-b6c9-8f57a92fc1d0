import { createAPI, createFileAPI, createUploadAPI, BASE_URL } from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//左侧
export const getAllCount = data => createAPI(BASE_URL + "/api/dp/getAllCount", 'get', data)

//县区 、 地图
export const getDwCount = data => createAPI(BASE_URL + "/api/dp/getDwCount", 'get', data)

//地图各市场所故障处理状态
export const getMaintenanceStatus = data => createAPI(BASE_URL + "/largeScreen/getMaintenanceStatus", 'get', data)


//评分排名
export const getPfList = data => createAPI(BASE_URL + "/api/dp/getPfList", 'get', data)

//下面列表
export const selectDwCount = data => createAPI(BASE_URL + "/api/dp/selectDwCount", 'get', data)

//饼图
export const selectPffb = data => createAPI(BASE_URL + "/api/dp/selectPffb", 'get', data)


export const toDwInterface = data => createAPI(BASE_URL + "/api/dp/toDwInterface", 'get', data)