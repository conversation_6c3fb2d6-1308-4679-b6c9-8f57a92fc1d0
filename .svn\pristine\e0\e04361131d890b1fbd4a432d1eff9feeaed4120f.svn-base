<template>
  <div class="bg_con" style="height: calc(100% - 38px)">
    <div
      style="width: 100%; position: relative; overflow: hidden; height: 100%"
    >
      <div class="dabg" style="height: 100%">
        <div class="content" style="height: 100%">
          <div class="table" style="height: 100%">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form
                :inline="true"
                :model="formInline"
                size="medium"
                class="demo-form-inline"
                style="float: left"
              >
                <el-form-item style="font-weight: 650" label="场所编号">
                  <el-input
                    v-model="formInline.computerRoomCode"
                    clearable
                    placeholder="场所编号"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 650" label="场所名称">
                  <el-input
                    v-model="formInline.computerRoomName"
                    clearable
                    placeholder="场所名称"
                  >
                  </el-input>
                </el-form-item>
                <!-- <el-form-item style="font-weight: 700" label="设备类型">
                  <el-input
                    v-model="formInline.zcbh"
                    clearable
                    placeholder="设备类型"
                  >
                  </el-input>
                </el-form-item> -->
                <el-form-item style="font-weight: 650" label="设备编号">
                  <el-input
                    v-model="formInline.equipmentCode"
                    clearable
                    placeholder="设备编号"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 650" label="设备名称">
                  <el-input
                    v-model="formInline.equipmentName"
                    clearable
                    placeholder="设备名称"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    @click="onSubmit"
                    >查询</el-button
                  >
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="warning"
                    icon="el-icon-circle-close"
                    @click="cz"
                    >重置</el-button
                  >
                </el-form-item>
                <div class="export-buttons">
                  <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      icon="el-icon-download"
                      @click="exportList()"
                      >导出
                    </el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="primary"
                      size="medium"
                      icon="el-icon-download"
                      @click="exportthisList()"
                      >导出当前页
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>

            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%">
              <div class="table_content" style="height: 100%">
                <el-table
                  :data="aqcpList"
                  border
                  @selection-change="selectRow"
                  :header-cell-style="{
                    background: '#EEF7FF',
                    color: '#4D91F8',
                  }"
                  style="width: 100%; border: 1px solid #ebeef5"
                  height="calc(100% - 131px)"
                  stripe
                >
                  <el-table-column
                    type="index"
                    width="60"
                    label="序号"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomCode"
                    label="场所编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="computerRoomName"
                    label="场所名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentCode"
                    label="设备编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentName"
                    label="设备名称"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="equipmentType"
                    label="设备类型"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="relocationLocation"
                    label="迁移地点"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="relocationTime"
                    label="迁移时间"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="relocationCode"
                    label="迁移人员编号"
                    sortable
                  ></el-table-column>
                  <el-table-column
                    prop="relocationName"
                    label="迁移人员名称"
                    sortable
                  ></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button
                        size="medium"
                        type="text"
                        @click="xqyl(scoped.row)"
                        >详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5">
                  <el-pagination
                    background
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    :pager-count="5"
                    :current-page="page"
                    :page-sizes="[5, 10, 20, 30]"
                    :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper"
                    :total="total"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog
          title="开始导入"
          class="scbg-dialog"
          width="600px"
          @close="mbxzgb"
          :visible.sync="dr_dialog"
          show-close
        >
          <div style="padding: 20px">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div class="drfs">二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1"
                  >追加（导入时已有的记录信息不变，只添加新的记录）</el-radio
                >
                <el-radio label="2"
                  >覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio
                >
              </el-radio-group>
            </div>
            <div class="daochu" v-if="uploadShow">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-upload
                :disabled="false"
                :http-request="uploadFile"
                action="/"
                :data="{}"
                class="upload-button"
                :show-file-list="false"
                :accept="accept"
                style="display: inline-block; margin-left: 20px"
              >
                <el-button size="small" type="primary">上传导入</el-button>
              </el-upload>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog
          width="1000px"
          height="800px"
          title="导入设备迁移"
          class="scbg-dialog"
          :visible.sync="dialogVisible_dr"
          show-close
        >
          <div style="height: 600px">
            <el-table
              :data="dr_cyz_list"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              style="width: 100%; border: 1px solid #ebeef5"
              height="100%"
              stripe
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column
                prop="cpmc"
                label="类型"
                :formatter="forlx"
              ></el-table-column>
              <el-table-column prop="sbxh" label="品牌型号"></el-table-column>
              <el-table-column prop="zcbh" label="资产编号"></el-table-column>
              <el-table-column prop="yt" label="用途"></el-table-column>
              <el-table-column prop="sl" label="数量"></el-table-column>
            </el-table>
          </div>

          <div
            style="
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 10px 0;
            "
          >
            <el-button type="primary" @click="drcy" size="mini"
              >导 入</el-button
            >
            <el-button
              type="warning"
              @click="dialogVisible_dr = false"
              size="mini"
              >关 闭</el-button
            >
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog
          title="新增设备迁移详细信息"
          :close-on-click-modal="false"
          :visible.sync="dialogVisible"
          width="47%"
          class="xg"
          :before-close="handleClose"
          @close="close('formName')"
        >
          <el-form
            ref="formName"
            :model="tjlist"
            :rules="rules"
            label-width="120px"
            size="mini"
          >
            <el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
              <el-input
                placeholder="资产编号"
                v-model="tjlist.zcbh"
                clearable
                @blur="onInputBlur(1)"
              >
              </el-input>
            </el-form-item>
            <div style="display: flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker
                  v-model="tjlist.qyrq"
                  clearable
                  type="date"
                  style="width: 100%"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="cpmc">
                <el-select
                  v-model="tjlist.cpmc"
                  placeholder="请选择类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in sblxxz"
                    :label="item.mc"
                    :value="item.id"
                    :key="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="品牌型号" prop="sbxh">
                <el-autocomplete
                  class="inline-input"
                  value-key="sbxh"
                  v-model.trim="tjlist.sbxh"
                  style="width: 100%"
                  :fetch-suggestions="querySearchsbxh"
                  placeholder="品牌型号"
                >
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-autocomplete
                  class="inline-input"
                  value-key="yt"
                  v-model.trim="tjlist.yt"
                  style="width: 100%"
                  :fetch-suggestions="querySearchyt"
                  placeholder="用途"
                >
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-autocomplete
                  class="inline-input"
                  value-key="azwz"
                  v-model.trim="tjlist.azwz"
                  style="width: 100%"
                  :fetch-suggestions="querySearchazwz"
                  placeholder="安装位置"
                >
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input
                  type="number"
                  placeholder="数量"
                  v-model="tjlist.sl"
                  clearable
                  :min="0"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')"
              >保 存</el-button
            >
            <el-button type="warning" @click="handleClose">关 闭</el-button>
          </span>
        </el-dialog>

        <el-dialog
          title="修改设备迁移详细信息"
          :close-on-click-modal="false"
          :visible.sync="xgdialogVisible"
          width="47%"
          class="xg"
          @close="close1('form')"
        >
          <el-form
            ref="form"
            :model="xglist"
            :rules="rules"
            label-width="120px"
            size="mini"
          >
            <el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
              <el-input
                placeholder="资产编号"
                v-model="xglist.zcbh"
                clearable
                @blur="onInputBlur(2)"
                disabled
              >
              </el-input>
            </el-form-item>
            <div style="display: flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker
                  v-model="xglist.qyrq"
                  style="width: 100%"
                  clearable
                  type="date"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="cpmc">
                <el-select
                  v-model="xglist.cpmc"
                  placeholder="请选择类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in sblxxz"
                    :label="item.mc"
                    :value="item.id"
                    :key="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="品牌型号" prop="sbxh">
                <el-autocomplete
                  class="inline-input"
                  value-key="sbxh"
                  v-model.trim="xglist.sbxh"
                  style="width: 100%"
                  :fetch-suggestions="querySearchsbxh"
                  placeholder="品牌型号"
                >
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-autocomplete
                  class="inline-input"
                  value-key="yt"
                  v-model.trim="xglist.yt"
                  style="width: 100%"
                  :fetch-suggestions="querySearchyt"
                  placeholder="用途"
                >
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-autocomplete
                  class="inline-input"
                  value-key="azwz"
                  v-model.trim="xglist.azwz"
                  style="width: 100%"
                  :fetch-suggestions="querySearchazwz"
                  placeholder="安装位置"
                >
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input
                  type="number"
                  placeholder="数量"
                  v-model="xglist.sl"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')"
              >保 存</el-button
            >
            <el-button type="warning" @click="xgdialogVisible = false"
              >关 闭</el-button
            >
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog
          title="设备迁移详细信息详情"
          :close-on-click-modal="false"
          :visible.sync="xqdialogVisible"
          width="47%"
          class="xg"
        >
          <el-form
            ref="form"
            :model="xglist"
            label-width="120px"
            size="mini"
            disabled
          >
            <el-form-item label="资产编号" prop="zcbh" class="one-line">
              <el-input
                placeholder="资产编号"
                v-model="xglist.zcbh"
                clearable
              ></el-input>
            </el-form-item>
            <div style="display: flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker
                  v-model="xglist.qyrq"
                  clearable
                  type="date"
                  style="width: 100%"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="cpmc">
                <el-select
                  v-model="xglist.cpmc"
                  placeholder="请选择类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in sblxxz"
                    :label="item.mc"
                    :value="item.id"
                    :key="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="品牌型号" prop="sbxh">
                <el-input
                  placeholder="品牌型号"
                  v-model="xglist.sbxh"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-input
                  placeholder="用途"
                  v-model="xglist.yt"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-input
                  placeholder="安装位置"
                  v-model="xglist.azwz"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input
                  type="number"
                  placeholder="数量"
                  v-model="xglist.sl"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false"
              >关 闭</el-button
            >
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import {
  // saveAqfhcp, //添加设备迁移
  // removeAqfhcp, //删除设备迁移
  // updateAqfhcp, //修改设备迁移
  // getAqfhcpList, //查询全部设备迁移带分页
  queryPage, //分页查询
} from "../../../../api/index";
import { getEquipmentType } from "../../../../api/shma";
//导入
import {
  //设备迁移导入模板
  downloadImportTemplateAqcp,
  //设备迁移模板上传解析
  uploadFileAqcp,
  //上传解析失败时 下载错误批注文件
  downloadXxsbError,
  //删除全部设备迁移
  deleteAllXxsb,
} from "../../../../api/drwj";
import { getAllAqcplx } from "../../../../api/xlxz";
import { getAllAqfhcp } from "../../../../api/all";
import { getCurAqfhcp } from "../../../../api/zhyl";
import {
  // 获取注册信息
  getDwxx,
} from "../../../../api/dwzc";
import { xxsbverify } from "../../../../api/jy";
import { exportXxsbData } from "../../../../api/dcwj";
import {
  sbqyexportAll, //导出全部
  sbqyexportOnePage, //导出当前页
} from "../../../../api/index";
export default {
  components: {},
  props: {},
  data() {
    return {
      zcbh: "", //资产编号
      pdaqcp: 0, //提示信息判断
      sblxxz: [], //下拉框数据
      aqcpList: [], //列表数据
      tableDataCopy: [], //查询备份数据
      xglist: {}, //修改与详情数据
      updateItemOld: {}, //修改，详情弹框信息
      xgdialogVisible: false, //修改弹框
      xqdialogVisible: false, //详情弹框
      formInline: {}, //查询区域数据
      tjlist: {
        zcbh: "",
        qyrq: "",
        cpmc: "",
        sbxh: "",
        yt: "",
        azwz: "",
        sl: "",
      }, //添加数据
      rules: {
        zcbh: [
          {
            required: true,
            message: "请输入资产编号",
            trigger: "blur",
          },
        ],
        qyrq: [
          {
            required: true,
            message: "请选择启用日期",
            trigger: "blur",
          },
        ],
        cpmc: [
          {
            required: true,
            message: "请选择类型",
            trigger: "blur",
          },
        ],
        sbxh: [
          {
            required: true,
            message: "请输入品牌型号",
            trigger: ["blur", "change"],
          },
        ],
        yt: [
          {
            required: true,
            message: "请输入用途",
            trigger: ["blur", "change"],
          },
        ],
        azwz: [
          {
            required: true,
            message: "请输入安装位置",
            trigger: ["blur", "change"],
          },
        ],
        sl: [
          {
            required: true,
            message: "请输入数量",
            trigger: "blur",
          },
        ],
      }, //校验
      page: 1, //当前页
      pageSize: 10, //每页条数
      total: 0, //总共数据数
      selectlistRow: [], //列表的值
      dialogVisible: false, //添加弹窗状态
      //导入
      dialogVisible_dr: false, //导入成员组弹窗状态
      dr_cyz_list: [], //待选择导入成员组列表
      multipleTable: [], //已选择导入成员组列表
      dwmc: "", //单位名称
      year: "", //年
      yue: "", //月
      ri: "", //日
      Date: "", //时间
      xh: [], //导出列表序号
      dclist: [], //复制列表数据
      dr_dialog: false,
      //数据导入方式
      sjdrfs: "",
      //获取单位信息数据
      dwxxList: {},
      //导入
      filename: "",
      form: {
        file: {},
      },
      accept: "", //接受文件格式
      dwjy: true,
      uploadShow: false, // 上传按钮显隐
      sblxList: [
        { id: 1, mc: "服务器" },
        { id: 2, mc: "交换机" },
        { id: 3, mc: "路由器" },
        { id: 4, mc: "专用设备" },
        { id: 5, mc: "波分设备" },
        { id: 6, mc: "分光器" },
        { id: 7, mc: "光纤" },
        { id: 8, mc: "网线" },
        { id: 9, mc: "电源线" },
      ],
      syztList: [
        { id: 1, mc: "正在使用" },
        { id: 2, mc: "停止使用" },
      ],
    };
  },
  computed: {},
  mounted() {
    this.getLogin();
    this.aqcp();
    this.sbzylxChange();
    this.smsblx();
    this.sbxhlist();
    this.zhsj();
    let anpd = localStorage.getItem("dwjy");
    console.log(anpd);
    if (anpd == 1) {
      this.dwjy = false;
    } else {
      this.dwjy = true;
    }
  },
  methods: {
    //详情弹框
    xqyl(row) {
      this.$router.push({
        path: "/sbqysjtjXqy",
        query: {
          id: row.id,
          routeType: "3",
          equipmentCode: row.equipmentCode,
          relocationId: row.relocationId,
        },
      });
    },
    ckls() {
      this.$router.push({
        path: "/lsAqcp",
      });
    },
    //获取登录信息
    async getLogin() {
      this.dwxxList = await getDwxx();
    },
    async smsblx() {
      this.sblxxz = await getAllAqcplx();
    },
    async zhsj() {
      let sj = await getCurAqfhcp();
      if (sj != "") {
        this.tjlist = sj;
      }
    },
    //新增数据按钮事件
    xzaqcp() {
      this.dialogVisible = true;
      this.aqcp();
    },
    //数据导入方式按钮事件
    Radio(val) {
      this.sjdrfs = val;
      console.log("当前选中的数据导入方式", val);
      if (this.sjdrfs != "") {
        this.uploadShow = true;
      }
    },
    mbxzgb() {
      this.sjdrfs = "";
    },
    async mbdc() {
      var returnData = await downloadImportTemplateAqcp();
      var date = new Date();
      var sj =
        date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate();
      this.dom_download(returnData, "设备迁移模板表-" + sj + ".xls");
    },
    //导入
    chooseFile() {},
    uploadFile(item) {
      this.form.file = item.file;
      console.log(this.form.file, "this.form.file");
      this.filename = item.file.name;
      console.log(this.filename, "this.filename");
      this.uploadZip();
    },

    async uploadZip() {
      let fd = new FormData();
      fd.append("file", this.form.file);
      let resData = await uploadFileAqcp(fd);
      console.log(resData);
      if (resData.code == 10000) {
        this.dr_cyz_list = resData.data;
        this.dialogVisible_dr = true;
        this.hide();
        //刷新表格数据
        // this.aqcp()
        this.$message({
          title: "提示",
          message: "上传成功",
          type: "success",
        });
      } else if (resData.code == 10001) {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
        this.$confirm(
          "[" + this.filename + "]中存在问题，是否下载错误批注文件？",
          "提示",
          {
            confirmButtonText: "下载",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(async () => {
            let returnData = await downloadXxsbError();
            this.dom_download(returnData, "设备迁移错误批注.xls");
          })
          .catch();
      } else if (resData.code == 10002) {
        this.$message({
          title: "提示",
          message: resData.message,
          type: "error",
        });
      }
    },
    //----成员组选择
    handleSelectionChange(val) {
      this.multipleTable = val;
      console.log("选中：", this.multipleTable);
    },
    //---确定导入成员组
    async drcy() {
      if (this.sjdrfs == 1) {
        this.multipleTable.forEach(async (item) => {
          let data = await saveAqfhcp(item);
          this.aqcp();
          console.log("data", data);
          if (data.code == 40004) {
            this.$message({
              title: "提示",
              message: data.message,
              type: "warning",
            });
          }
        });
        this.dialogVisible_dr = false;
      } else if (this.sjdrfs == 2) {
        this.dclist = await getAllAqfhcp();
        deleteAllXxsb(this.dclist);
        setTimeout(() => {
          this.multipleTable.forEach(async (item) => {
            let data = await saveAqfhcp(item);
            this.aqcp();
            console.log("data", data);
          });
        }, 500);
        this.dialogVisible_dr = false;
      }
      this.uploadShow = false;
      this.dr_dialog = false;
    },
    //隐藏
    hide() {
      this.filename = null;
      this.form.file = {};
    },
    //----表格导入方法
    readExcel(e) {},
    //修改
    updataDialog(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let that = this;
          updateAqfhcp(this.xglist).then(() => {
            that.aqcp();
            that.sbxhlist();
          });

          // 关闭dialog
          this.$message.success("修改成功");
          this.xgdialogVisible = false;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // //详情弹框
    // xqyl(row) {
    //   this.updateItemOld = JSON.parse(JSON.stringify(row));

    //   this.xglist = JSON.parse(JSON.stringify(row));
    //   // this.form1.ywlx = row.ywlx
    //   console.log("old", row);
    //   console.log("this.xglist.ywlx", this.xglist);
    //   this.xqdialogVisible = true;
    // },
    //修改弹框
    updateItem(row) {
      this.updateItemOld = JSON.parse(JSON.stringify(row));
      this.xglist = JSON.parse(JSON.stringify(row));
      // this.form1.ywlx = row.ywlx
      console.log("old", row);
      console.log("this.xglist.ywlx", this.xglist);
      this.xgdialogVisible = true;
    },
    //查询
    onSubmit() {
      this.page = 1;
      this.aqcp();
      // //  form是查询条件
      // console.log(this.formInline);
      // // 备份了一下数据
      // let arr = this.tableDataCopy
      // // 通过遍历key值来循环处理
      // Object.keys(this.formInline).forEach(e => {
      // 	// 调用自己定义好的筛选方法
      // 	console.log(this.formInline[e]);
      // 	arr = this.filterFunc(this.formInline[e], e, arr)
      // })
      // // 为表格赋值
      // this.aqcpList = arr
    },
    //查询方法
    filterFunc(val, target, filterArr) {
      // 参数不存在或为空时，就相当于查询全部
    },

    returnSy() {
      this.$router.push("/tzglsy");
    },
    //获取列表的值
    async aqcp() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        computerRoomCode: this.formInline.computerRoomCode,
        computerRoomName: this.formInline.computerRoomName,
        equipmentType: this.formInline.equipmentType,
        equipmentCode: this.formInline.equipmentCode,
        equipmentName: this.formInline.equipmentName,
        equipmentMainType: this.formInline.equipmentMainType,
      };
      let resList = await queryPage(params);
      console.log("params", params);
      this.aqcpList = resList.data.records;
      this.total = resList.data.total;
    },
    async sbzylxChange() {
      let data = await getEquipmentType();
      this.sblxList = data.data;
    },
    //删除
    shanchu(id) {
      let that = this;
      if (this.selectlistRow != "") {
        this.$confirm("是否继续删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let valArr = this.selectlistRow;
            // console.log("....", val);
            valArr.forEach(function (item) {
              let params = {
                jlid: item.jlid,
                dwid: item.dwid,
              };
              removeAqfhcp(params).then(() => {
                that.aqcp();
                that.sbxhlist();
              });
              console.log("删除：", item);
              console.log("删除：", item);
            });
            // let params = valArr;
            this.$message({
              message: "删除成功",
              type: "success",
            });
          })
          .catch(() => {
            this.$message("已取消删除");
          });
      } else {
        this.$message({
          message: "未选择删除记录，请选择下列列表",
          type: "warning",
        });
      }
    },
    //添加
    showDialog() {
      this.dialogVisible = true;
      console.log(1111);
    },

    //导出
    async exportList() {
      // var param = {
      //   computerRoomCode: this.formInline.computerRoomCode,
      //   computerRoomName: this.formInline.computerRoomName,
      //   equipmentType: this.formInline.equipmentType,
      //   equipmentCode: this.formInline.equipmentCode,
      //   equipmentName: this.formInline.equipmentName,
      //   equipmentMainType: this.formInline.equipmentMainType,
      // };

      let formData = new FormData();
      if (this.formInline.computerRoomCode) {
        formData.append("computerRoomCode", this.formInline.computerRoomCode);
      }
      if (this.formInline.computerRoomName) {
        formData.append("computerRoomName", this.formInline.computerRoomName);
      }
      if (this.formInline.equipmentMainType) {
        formData.append("equipmentMainType", this.formInline.equipmentMainType);
      }
      if (this.formInline.equipmentType) {
        formData.append("equipmentType", this.formInline.equipmentType);
      }
      if (this.formInline.equipmentCode) {
        formData.append("equipmentCode", this.formInline.equipmentCode);
      }
      if (this.formInline.equipmentName) {
        formData.append("equipmentName", this.formInline.equipmentName);
      }
      if (
        this.formInline.lastInspectionTime &&
        this.formInline.lastInspectionTime.length === 2
      ) {
        formData.append("qssj", this.formInline.lastInspectionTime[0]);
        formData.append("jssj", this.formInline.lastInspectionTime[1]);
      }

      var returnData = await sbqyexportAll(formData);
      var date = new Date();
      var sj =
        date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate();
      this.dom_download(returnData, "设备迁移结果-" + sj + ".xlsx");
    },
    //导出当前页
    async exportthisList() {
      let formData = new FormData();

      if (this.formInline.computerRoomCode) {
        formData.append("computerRoomCode", this.formInline.computerRoomCode);
      }
      if (this.formInline.computerRoomName) {
        formData.append("computerRoomName", this.formInline.computerRoomName);
      }
      if (this.formInline.equipmentMainType) {
        formData.append("equipmentMainType", this.formInline.equipmentMainType);
      }
      if (this.formInline.equipmentType) {
        formData.append("equipmentType", this.formInline.equipmentType);
      }
      if (this.formInline.equipmentCode) {
        formData.append("equipmentCode", this.formInline.equipmentCode);
      }
      if (this.formInline.equipmentName) {
        formData.append("equipmentName", this.formInline.equipmentName);
      }
      formData.append("pageNo", this.page);
      formData.append("pageSize", this.pageSize);

      if (
        this.formInline.lastInspectionTime &&
        this.formInline.lastInspectionTime.length === 2
      ) {
        formData.append("qssj", this.formInline.lastInspectionTime[0]);
        formData.append("jssj", this.formInline.lastInspectionTime[1]);
      }

      var returnData = await sbqyexportOnePage(formData);
      var date = new Date();
      var sj =
        date.getFullYear() + "" + (date.getMonth() + 1) + "" + date.getDate();
      this.dom_download(returnData, "设备迁移结果-" + sj + ".xlsx");
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    //确定添加成员组
    submitTj(formName) {
      if (this.tjlist.sl > 0) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let params = {
              dwid: this.dwxxList.dwid,
              zcbh: this.tjlist.zcbh,
              qyrq: this.tjlist.qyrq,
              cpmc: this.tjlist.cpmc,
              sbxh: this.tjlist.sbxh,
              yt: this.tjlist.yt,
              azwz: this.tjlist.azwz,
              sl: this.tjlist.sl,
              cjrid: this.dwxxList.cjrid,
              cjrxm: this.dwxxList.cjrxm,
              // aqcpid: getUuid()
            };
            this.onInputBlur(1);
            if (this.pdaqcp.code == 10000) {
              let that = this;
              saveAqfhcp(params).then(function () {
                // that.resetForm();
                that.aqcp();
                that.sbxhlist();
              });
              this.dialogVisible = false;

              this.$message({
                message: "添加成功",
                type: "success",
              });
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        this.$message.warning("数量不可为负数！");
      }
    },

    deleteTkglBtn() {},
    //选中列表的数据
    selectRow(val) {
      console.log(val);
      this.selectlistRow = val;
    },
    //列表分页--跳转页数
    handleCurrentChange(val) {
      this.page = val;
      this.aqcp();
    },
    //列表分页--更改每页显示个数
    handleSizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.aqcp();
    },
    //添加重置
    resetForm() {
      this.tjlist.mc = "";
      this.tjlist.zcbh = "";
      this.tjlist.qyrq = "";
      this.tjlist.lx = "";
      this.tjlist.sbxh = "";
      this.tjlist.yt = "";
      this.tjlist.azwz = "";
      this.tjlist.sl = "";
    },
    handleClose(done) {
      this.resetForm();
      this.dialogVisible = false;
    },
    // 弹框关闭触发
    close(formName) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[formName].resetFields();
    },
    //取消校验
    close1(form) {
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs[form].resetFields();
    },
    //添加时的校验
    async onInputBlur(index) {
      if (index == 1) {
        let params = {
          // bmbh: this.tjlist.bmbh,
          zcbh: this.tjlist.zcbh,
          // zjxlh: this.tjlist.zjxlh
        };
        this.pdaqcp = await xxsbverify(params);
        console.log(this.pdaqcp);
        if (this.pdaqcp.code == 40003) {
          this.$message.error("保密编号已存在");
          return;
        } else if (this.pdaqcp.code == 40004) {
          this.$message.error("资产编号已存在");
          return;
        } else if (this.pdaqcp.code == 40005) {
          this.$message.error("主机序列号已存在");
          return;
        }
      }
    },
    //模糊查询品牌型号
    querySearchsbxh(queryString, cb) {
      var restaurants = this.restaurantssbxh;
      console.log("restaurants", restaurants);
      var results = queryString
        ? restaurants.filter(this.createFiltersbxh(queryString))
        : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].sbxh === results[j].sbxh) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFiltersbxh(queryString) {
      return (restaurant) => {
        return (
          restaurant.sbxh.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        );
      };
    },
    //模糊查询用途
    querySearchyt(queryString, cb) {
      var restaurants = this.restaurantssbxh;
      console.log("restaurants", restaurants);
      var results = queryString
        ? restaurants.filter(this.createFilteryt(queryString))
        : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].yt === results[j].yt) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilteryt(queryString) {
      return (restaurant) => {
        return (
          restaurant.yt.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        );
      };
    },
    //模糊查询安装位置
    querySearchazwz(queryString, cb) {
      var restaurants = this.restaurantssbxh;
      console.log("restaurants", restaurants);
      var results = queryString
        ? restaurants.filter(this.createFilterazwz(queryString))
        : restaurants;
      console.log("results", results);
      // 调用 callback 返回建议列表的数据
      for (var i = 0; i < results.length; i++) {
        for (var j = i + 1; j < results.length; j++) {
          if (results[i].azwz === results[j].azwz) {
            results.splice(j, 1);
            j--;
          }
        }
      }
      cb(results);
      console.log("cb(results.zw)", results);
    },
    createFilterazwz(queryString) {
      return (restaurant) => {
        return (
          restaurant.azwz.toLowerCase().indexOf(queryString.toLowerCase()) > -1
        );
      };
    },
    async sbxhlist() {
      let resList = await getAllAqfhcp();
      this.restaurantssbxh = resList;
    },
    cz() {
      this.formInline = {};
    },
    forlx(row) {
      let hxsj;
      this.syztList.forEach((item) => {
        if (row.useStatus == item.id) {
          hxsj = item.mc;
        }
      });
      return hxsj;
    },
  },
  watch: {},
};
</script>

<style scoped>
.bg_con {
  width: 100%;
}

.dabg {
  /* margin-top: 10px; */
  box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
  border-radius: 8px;
  width: 100%;
}

.daochu {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

.xmlb-title {
  line-height: 60px;
  width: 100%;
  padding-left: 10px;
  height: 60px;
  background: url(../../../assets/background/bg-02.png) no-repeat left;
  background-size: 100% 100%;
  text-indent: 10px;
  /* margin: 0 20px; */
  color: #0646bf;
  font-weight: 700;
}

.fhsy {
  display: inline-block;
  width: 120px;
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 30px;
  padding-top: 4px;
  float: right;
  background: url(../../../assets/icons/zzjg_icon1.png) no-repeat center;
  background-size: 100% 100%;
}

.item_button {
  height: 100%;
  float: left;
  padding-left: 10px;
  line-height: 50px;
}

.select_wrap {
  /* //padding: 5px; */

  .select_wrap_content {
    float: left;
    width: 100%;
    line-height: 50px;
    /* // padding-left: 20px; */
    /* // padding-right: 20px; */
    height: 100%;
    background: rgba(255, 255, 255, 0.7);

    .item_label {
      padding-left: 10px;
      height: 100%;
      float: left;
      line-height: 50px;
      font-size: 1em;
    }
  }
}

.mhcx1 {
  margin-top: 0px;
}

.widths {
  width: 6vw;
}

.cd {
  width: 191px;
}

/deep/.el-form--inline .el-form-item {
  margin-right: 9px;
}

/deep/.mhcx .el-form-item {
  /* margin-top: 5px; */
  margin-bottom: 5px;
}

.dialog-footer {
  display: block;
  margin-top: 10px;
}

.export-buttons {
  display: flex; /* 使按钮水平排列 */
  align-items: center; /* 垂直居中对齐 */
  margin-top: 10px; /* 根据需要调整间距 */
}
</style>
