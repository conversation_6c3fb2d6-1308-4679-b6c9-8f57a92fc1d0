<template>
  <div class="login clearfix">
    <div style="height: 100%; display: flex; flex-flow: column">
      <div style="flex: 1">
        <div class="indexLogo">
          <!-- <div class="bg-bm">
            <div class="guohui">
              <img src="../assets/logo/logo.png" alt="" />
              &nbsp;&nbsp;&nbsp;&nbsp;<img
                src="./images/logo2.png"
                style="width: 120px; height: 48px"
                alt=""
              />
            </div>
          </div> -->
          <div class="bg-bm">
            <div v-if="loginStatus">
              <div class="indexR" v-if="zc">
                <div class="indexR-title">账号密码登录</div>
                <div class="content">
                  <div class="regform-use" id="userId">
                    <div class="zsdlinput-title">账号</div>
                    <div class="zsdlinput">
                      <span class="fl se_wai"
                        ><input
                          type="text"
                          v-model="username"
                          name="username"
                          id="username"
                          maxlength="20"
                          class="dlinput"
                          placeholder="请输入用户名"
                      /></span>
                    </div>
                    <div class="zsdlinput-title">密码</div>
                    <div class="zsdlinput">
                      <span class="fl se_wai"
                        ><input
                          type="password"
                          v-model="password"
                          name="password"
                          maxlength="20"
                          id="password"
                          class="dlinput"
                          placeholder="请输入密码"
                          autocomplete="off"
                          @keyup.enter="loginIt"
                      /></span>
                    </div>
                    <div style="margin-top: 10%">
                      <div class="dlinputd">
                        <input
                          ref="aaa"
                          type="button"
                          value="登       录"
                          @click="loginIt"
                        />
                      </div>
                      <div class="zcinputd" v-if="!zcan">
                        <input
                          ref="aaa"
                          type="button"
                          value="单位注册"
                          @click="loginZc"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <dwzc v-else @baocun="baocun" @quxiao="quxiao"></dwzc>
            </div>
            <div class="indexLoading" v-else>正在登录中，请稍后...</div>
          </div>

          <div
            class="footer"
            style="
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 14px;
            "
          >
            技术支持：哈尔滨思和信息技术股份有限公司
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dwzc from "./components/dwzc.vue";
import {
  // 判断单位是否已经注册
  verifyRegiste,
  login,
  getUserInfo,
} from "../../api/dwzc";
import { checkArr } from "../../utils/utils";
import store from "../store";
import { mapGetters, mapActions } from "vuex";
// 操作浏览器本地数据
import {
  setWindowLocation,
  getWindowLocation,
} from "../../utils/windowLocation.js";
// import PubSub from 'pubsub-js'
export default {
  components: {
    dwzc,
  },
  data() {
    return {
      username: "",
      password: "",
      radio: "1",
      yhmc: "",
      zc: true,
      zcan: true,
      dwxx2: [],
      userToken: "",
      loginStatus: true, // 登录状态
    };
  },
  mounted() {
    // 接值
    const params = new URLSearchParams(window.location.search);
    const userName = params.get("userName");
    const password = params.get("userId");
    if (userName && password) {
      this.loginStatus = false;
    } else {
      this.loginStatus = true;
    }
    if (this.loginStatus == false) {
      this.loginLoading(userName, password);
    }
    this.registe();
  },
  methods: {
    // loading登陆方法
    async loginLoading(userName, password) {
      let params = {
        userName: userName,
        passWord: password,
      };
      login(params).then(async (res) => {
        if (res.code === 10000) {
          store.commit("addNewToken", res.data);
          let dataLogin = await getUserInfo();
          const PubSub = require("pubsub-js");
          PubSub.publish("data", dataLogin);
          console.log(dataLogin, "1222222222222222222222222");
          if (dataLogin.dwlx2 && dataLogin.dwlx2 == 1) {
            this.$router.push("/homePage"); //登录验证成功路由实现跳转
          } else {
            this.$router.push("/ztqksy");
          }
          // this.$router.push('/homePage')	//登录验证成功路由实现跳转

          console.log("登录成功");
        } else {
          console.log("登录失败");
          this.$message.warning("用户名或密码错误！！！");
        }
      });
    },
    // 退出系统
    quitSystem() {
      // this.$confirm('是否执行此操作，点击将退出系统？', '是否退出系统？', {
      //   cancelButtonClass: "btn-custom-cancel",
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      //   // center: true
      // }).then(() => {
      //   this.$electron.ipcRenderer.send("close")
      // }).catch(() => {
      //   this.$message({
      //     type: 'info',
      //     message: '取消退出系统'
      //   })
      // })
    },
    dwxx() {},
    //判断是否注册
    async registe() {
      this.zcan = await verifyRegiste();
      // this.zcan = false
      // console.log("this.zc this.zc this.zc this.zc this.zc ", this.zcan);
    },
    baocun(value) {
      // this.dwxx()
      this.zc = value;
    },
    quxiao(value) {
      this.zc = value;
    },
    async loginIt() {
      let that = this;
      if (this.username.trim() == "") {
        this.$message.warning("请输入用户名");
        return;
      }
      if (this.password.trim() == "") {
        this.$message.warning("请输入密码");
        return;
      }
      let params = {
        userName: this.username,
        passWord: this.password,
      };
      login(params).then(async (res) => {
        console.log(res); //请求成功后执行函数
        if (res.code === 10000) {
          //利用localstorage存储到本地
          // store.dispatch("addToken", res.data);
          store.commit("addNewToken", res.data);
          // localStorage.setItem("user-token", res.data)
          // this.userToken = "Bearer " + res.data;
          let dataLogin = await getUserInfo();
          console.log(dataLogin);
          const PubSub = require("pubsub-js");
          PubSub.publish("data", dataLogin);
          if (
            (dataLogin.dwlx2 && dataLogin.dwlx2 == 1) ||
            (dataLogin.dwlx2 && dataLogin.dwlx2 == 2)
          ) {
            this.$router.push("/homePage"); //登录验证成功路由实现跳转
          } else {
            this.$router.push("/ztqksy");
          }
          // if (dataLogin.dwlx2 == 1 || dataLogin.dwlx2 == 2) {
          //   this.$router.push('/homePage')	//登录验证成功路由实现跳转
          // } else if (dataLogin.dwlx2 == 3) {
          //   this.$router.push('/ztqksy')
          // }
          // this.$router.push('/homePage')	//登录验证成功路由实现跳转

          console.log("登录成功");
        } else {
          console.log("登录失败");
          this.$message.warning("用户名或密码错误！！！");
        }
      });
    },
    loginZc() {
      this.zc = false;
    },
  },

  watch: {},
};
</script>

<style scoped>
/** i标签图标样式 **/
.i-icon {
  font-size: 24px;
  margin: 0 12px;
}

/***/
.login {
  height: 92%;
  width: 100%;
  margin: 0;
  padding: 0;
}

.login_left .left_text {
  height: 85px;
  line-height: 85px;
  margin-top: 25px;
  text-align: center;
  padding-left: 90px;
  color: #000;
  font-weight: 500;
  font-size: 16px;
}

.login_left .left_img {
  text-align: center;
  padding-left: 90px;
}

.login_left .left_img img {
  width: 154px;
  height: 154px;
}

.login_content {
  width: 30%;
  /* height: 55%; */
  margin-top: 10%;
  padding-top: 15px;
  border: 1px solid #cfd3e0;
  border-radius: 5px;
  margin-left: 35%;
  /* float: right; */
}

.login_left {
  width: 40%;
  margin-left: 12%;
  height: 50%;
  margin-top: 10%;
  float: left;
}

.login_content .login_title {
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #4c4949;
  font-weight: 500;
  font-size: 16px;
}
/deep/.el-main {
  background-image: url(../assets/background/登录页.jpg) !important;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
/* .login_content .login_title img {
  height: 100%;
  width: 100%;
} */
.loginTabs {
  height: 40px;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid #cfd3e0;
}

.tabsLeft {
  width: 50%;
  float: left;
}

/* 注册 */
.register_container {
  width: 100%;
  height: 100%;
}

.zczh {
  width: 40%;
  margin: auto;
}

.zczh_ts {
  font-size: 14px;
  color: #666;
  height: 65px;
  line-height: 65px;
}

.hqyzmBtn {
  width: 28%;
  float: right;
  margin-top: 4px;
  cursor: pointer;
  /* width: 94px; */
  height: 33px;
  line-height: 33px;
  text-align: center;
  font-size: 14px;
  background: #efefef;
  color: #333;
  border: 1px solid #dddee3;
  border-radius: 3px;
}

.next_xb {
  width: 50%;
  height: 33px;
  line-height: 33px;
  text-align: center;
  font-size: 14px;
  border: 1px solid #bfc0c2;
  background: #efefef;
  color: #2b3642;
  cursor: pointer;
  margin-left: 122px;
}

.auth_text_blue {
  color: #c7cad5;
}

.wc_bord {
  height: 105px;
  width: 470px;
  overflow: hidden;
  margin-top: 135px;
}

.wcb_left {
  float: left;
  width: 105px;
  height: 100%;
}

.wcb_left img {
  width: 100%;
  height: 100%;
}

.wcb_right {
  float: left;
  height: 100%;
  margin-left: 20px;
}

/* ---------------------------*/
a,
a:active,
a:link,
a:visited {
}

.indexLogo {
  width: 100%;
  /* min-width: 1000px; */
  height: 100%;
  /* background: url(./images/bgcard.png) no-repeat center;
  background-color: #f5f5f6; */
  background-color: rgba(255, 255, 255, 0);
  background-size: 100% 100%;
  clear: both;
  position: relative;
}

.bg-bm {
  width: calc(100vw * 1240 / 1920);
  height: calc(100vh * 680 / 1080);
  position: absolute;
  background: url(./images/login-box.png) no-repeat center;
  background-size: 100% 100%;
  top: calc(100vh * 196 / 1080);
  left: calc(100vw * 340 / 1920);
}

.indexR {
  width: calc(100vw * 490 / 1920);
  height: calc(100vh * 645 / 1080);
  /* margin: 6% 13% 0 0; */
  /* float: right; */
  position: absolute;
  top: calc(100vh * 15 / 1080);
  right: calc(100vw * 20 / 1920);
  /* top: 20%;
  right: 26%; */
  /* background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.40) 50%); */
  /* box-shadow: 0px 2px 20px 0px rgba(45, 93, 194, 0.5); */
  /* border-radius: 8px; */
  /* background: #fff; */
  /* background: rgba(255, 255, 255, 0.92); */
  z-index: 99;
}
.indexR-title {
  font-family: PingFangSC-Semibold;
  font-size: calc(100vw * 22 / 1920);
  color: #1e1e1e;
  letter-spacing: 0;
  font-weight: 600;
  margin-top: calc(100vh * 84 / 1080);
  margin-left: calc(100vw * 60 / 1920);
}

.indexLoading {
  width: 25%;
  height: 62%;
  position: absolute;
  top: 48%;
  right: 26%;
  color: #ffffff;
  font-size: 28px;
}

.top-font {
  font-size: 20px;
  color: #737c90;
  letter-spacing: 0;
  text-align: center;
  line-height: 28px;
  font-weight: 400;
}

.guohui {
  width: 50%;
  height: 20%;
  /* background: #000; */
  display: flex;
  align-items: center;

  margin: 4% 0 0 4%;
  /* justify-content: center; */
}

.guohui img {
  width: 3.91vw;
  height: 4.2vw;
}

.guohui span {
  font-family: AlibabaPuHuiTi-Bold;
  font-size: 2vw;
  color: #fff;
  letter-spacing: 0;
  font-weight: 700;
  margin-left: 10;
}

.content {
  width: 100%;
  height: 80%;
  /* background-color: rgba(255,255,255,0.40); */
  z-index: 99;
}

.regform-use {
  width: 78%;
  height: 70%;
  /* margin: 0 auto; */
  /* background: #ffffff; */
  border-radius: 8px;
  padding-top: calc(100vh * 60 / 1080);
  margin-left: calc(100vw * 60 / 1920);
  /* margin-top: 1.5vw; */
}

.zsdlinput {
  border-bottom: #d3d5d6 1px solid;
  width: 100%;
  height: calc(100vh * 50 / 1080);
  margin-bottom: calc(100vh * 70 / 1080);

  /* background: #fff; */
  display: flex;
  align-items: center;
}
.zsdlinput-title {
  font-family: PingFangSC-Semibold;
  font-size: 16px;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
  margin-bottom: calc(100vh * 7 / 1080);
}
span.fl {
  float: left;
  display: inline-block;
}

span.fl img {
  padding: 0 10px 0 10px;
  width: 16px;
}

.fr {
  float: right;
}

.se_wai {
  border: 0;
  border-left: 0px;
  height: 2vw;
  width: 90%;
  overflow: hidden;
  /* background: #fff; */
}

.dlinput {
  border: 0;
  /* padding-top: 2px; */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  outline: none;
}

.qing269 {
  color: #2695fe;
  font: 12px/1.5 Microsoft YaHei, tahoma, arial, Hiragino Sans GB, \\5b8b\4f53,
    sans-serif;
}

.qing269 a {
  color: #316ba2;
}

.msg a {
  color: #d30b15;
}

.dlinputd {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  /* margin-top: 10%; */
}

.zcinputd {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  /* background: #21A566; */
  margin-top: 3%;
}

.dlinputd input {
  border: 0px;
  background-image: linear-gradient(269deg, #0544c0 1%, #368fd5 100%);
  box-shadow: 0px 16px 10px -10px rgba(0, 74, 160, 0.3);
  width: 100%;
  height: 2.5rem;
  line-height: 2.5rem;
  color: #fff;
  cursor: pointer;
}

.footer {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
}

.zcinputd input {
  border: 0px;
  background: #21a566;
  border-radius: 4px;
  width: 84%;
  height: 2.5rem;
  line-height: 2.5rem;
  color: #fff;
  cursor: pointer;
}

.footer-wrapper {
  border-top: 8px solid #fff;
  background: #fff;
  border-bottom: 1px solid #fff;
  height: 140px;
  padding-top: 20px;
}

.footer-wrapper .f-footer {
  width: 1000px;
  margin: 14px auto;
  margin-top: 10px;
}

.clearfix {
  zoom: 1;
}

.footer-wrapper .f-footer > div {
  text-align: center;
}

.footer-wrapper .f-footer .f-f-icon a:first-child {
  margin-top: 14px;
  margin-left: 40px;
}

.footer-wrapper .f-footer .f-f-icon a {
  display: block;
  float: left;
  margin: 0;
}

.footer-wrapper .f-footer .f-links ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.footer-wrapper .f-footer .f-links ul li {
  list-style: none;
  float: left;
  margin: 0;
  padding: 5px 0;
}

.footer-wrapper .f-footer .f-links ul li a {
  color: #282828;
  text-decoration: none;
  font-weight: 700;
  padding: 0 13px;
  font-size: 14px;
  position: relative;
}

.footer-wrapper .f-footer .f-zhuban {
  font-size: 12px;
  padding: 0px;
}

.footer-wrapper .f-footer .f-zhuban:last-child {
  color: #3c6f99;
}

.footer-wrapper .f-footer .f-zhuban span {
  margin: 0 8px;
  height: 20px;
  line-height: 20px;
}

.footer-wrapper .f-footer .f-zhuban span {
  margin: 0 8px;
  height: 20px;
  line-height: 20px;
}
</style>
<style>
.el-input__inner {
  /* border: 0.5px solid #505050 !important; */
  /* border-radius: 0 !important; */
  color: #000 !important;
}

.el-input__prefix,
.el-input__suffix {
  color: #000 !important;
}

.el-form-item__error {
  margin-left: 15px;
}

.el-step.is-simple .el-step__icon {
  margin-top: 5px;
}

.el-radio .el-radio__label {
  /* font-size: 24px !important; */
  font-weight: 500;
  letter-spacing: 0;
}
</style>